<include file="block/hat" />
<style>
/* 招聘系统专用样式 */
.recruitment-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 8px 8px 0 0;
    margin-bottom: 0;
}

.recruitment-header h4 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.recruitment-header .btn {
    background: rgba(255,255,255,0.2);
    border: 1px solid rgba(255,255,255,0.3);
    color: white;
    transition: all 0.3s ease;
}

.recruitment-header .btn:hover {
    background: rgba(255,255,255,0.3);
    border-color: rgba(255,255,255,0.5);
    color: white;
    transform: translateY(-1px);
}

.search-panel {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 20px;
    margin-bottom: 20px;
}

.search-panel .form-control {
    border-radius: 4px;
    border: 1px solid #ddd;
    transition: all 0.3s ease;
}

.search-panel .form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.table-container {
    background: white;
    border-radius: 0 0 8px 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.modern-table {
    margin-bottom: 0;
}

.modern-table thead th {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 2px solid #667eea;
    font-weight: 600;
    color: #495057;
    padding: 15px 12px;
    font-size: 14px;
}

.modern-table tbody tr {
    transition: all 0.3s ease;
    border-bottom: 1px solid #f1f3f4;
}

.modern-table tbody tr:hover {
    background: linear-gradient(90deg, rgba(102, 126, 234, 0.05) 0%, rgba(102, 126, 234, 0.02) 100%);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.modern-table tbody td {
    padding: 12px 8px;
    vertical-align: middle;
    border-top: none;
}

.modern-table tbody td:last-child {
    padding: 8px 12px;
}

.notice-title {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 3px;
    font-size: 13px;
    line-height: 1.3;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.notice-description {
    color: #6c757d;
    font-size: 11px;
    line-height: 1.3;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 240px;
}

.status-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-active {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
}

.status-inactive {
    background: linear-gradient(135deg, #6c757d 0%, #adb5bd 100%);
    color: white;
}

.post-count-badge {
    background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
    color: white;
    padding: 4px 10px;
    border-radius: 15px;
    font-size: 11px;
    font-weight: 500;
}

.action-buttons {
    display: block;
    width: 100%;
}

.action-buttons .btn {
    padding: 6px 10px;
    font-size: 10px;
    border-radius: 3px;
    transition: all 0.3s ease;
    border: none;
    min-width: auto;
    height: auto;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    text-decoration: none;
    white-space: nowrap;
    margin-bottom: 2px;
}

.action-buttons .btn i {
    margin-right: 3px;
    font-size: 9px;
}

.action-buttons .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.25);
    text-decoration: none;
}

.btn-edit {
    background: #007bff;
    color: white;
}
.btn-edit:hover {
    background: #0056b3;
    color: white;
}

.btn-config {
    background: #17a2b8;
    color: white;
}
.btn-config:hover {
    background: #117a8b;
    color: white;
}

.btn-filter {
    background: #ffc107;
    color: #212529;
}
.btn-filter:hover {
    background: #e0a800;
    color: #212529;
}

.btn-view {
    background: #28a745;
    color: white;
}
.btn-view:hover {
    background: #1e7e34;
    color: white;
}

.btn-toggle {
    background: #6c757d;
    color: white;
}
.btn-toggle:hover {
    background: #545b62;
    color: white;
}

.btn-delete {
    background: #dc3545;
    color: white;
}
.btn-delete:hover {
    background: #c82333;
    color: white;
}

/* 按钮组整体样式 */
.action-buttons {
    min-height: 45px;
    padding: 3px 0;
}

/* 按钮激活状态 */
.action-buttons .btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

/* 按钮禁用状态 */
.action-buttons .btn:disabled,
.action-buttons .btn.disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* 按钮文字不可选择 */
.action-buttons .btn {
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .action-buttons {
        flex-direction: column;
        gap: 3px;
    }

    .action-buttons .btn {
        width: 100%;
        justify-content: flex-start;
    }

    .notice-title {
        font-size: 12px;
    }

    .notice-description {
        font-size: 10px;
        max-width: 200px;
    }
}

/* 公告信息悬停效果 */
.notice-title:hover,
.notice-description:hover {
    color: #667eea;
    cursor: help;
}

.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #6c757d;
}

.empty-state i {
    font-size: 48px;
    margin-bottom: 20px;
    opacity: 0.5;
}

.pagination-wrapper {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 0 0 8px 8px;
    border-top: 1px solid #e9ecef;
}
</style>

<div class="container-fluid">
    <div class="row">
        <include file="block/menu" />
        <div class="col-xs-12 col-sm-9 col-lg-10">
            <div class="main">
                <div class="panel panel-default" style="border: none; box-shadow: 0 4px 20px rgba(0,0,0,0.1); border-radius: 8px;">
                    <div class="recruitment-header">
                        <div class="row">
                            <div class="col-md-6">
                                <h4>
                                    <i class="fa fa-bullhorn"></i> 招聘公告管理
                                    <small style="opacity: 0.8; margin-left: 10px;">智能筛选系统</small>
                                </h4>
                            </div>
                            <div class="col-md-6 text-right">
                                <a href="{:U('add')}" class="btn btn-sm" style="padding: 10px 20px; font-size: 13px; font-weight: 600;">
                                    <i class="fa fa-plus-circle"></i> 添加公告
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- 搜索表单 -->
                    <div class="search-panel">
                        <form class="form-inline" method="get" action="{:U('index')}">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-group" style="width: 100%;">
                                        <label class="sr-only">搜索字段</label>
                                        <select name="kw" class="form-control" style="width: 100%;">
                                            <volist name="c_kw" id="v" key="k">
                                                <option value="{$k}" <if condition="$s_kw eq $k">selected</if>>{$v}</option>
                                            </volist>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group" style="width: 100%;">
                                        <label class="sr-only">搜索关键词</label>
                                        <input type="text" name="val" value="{$s_val}" class="form-control"
                                               placeholder="请输入搜索关键词..." style="width: 100%;">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group" style="width: 100%;">
                                        <label class="sr-only">状态筛选</label>
                                        <select name="status" class="form-control" style="width: 100%;">
                                            <option value="">全部状态</option>
                                            <option value="1" <if condition="$s_status eq '1'">selected</if>>启用</option>
                                            <option value="0" <if condition="$s_status eq '0'">selected</if>>停用</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="btn-group" style="width: 100%;">
                                        <button type="submit" class="btn btn-primary" style="border-radius: 4px 0 0 4px;">
                                            <i class="fa fa-search"></i> 搜索
                                        </button>
                                        <a href="{:U('index')}" class="btn btn-default" style="border-radius: 0 4px 4px 0;">
                                            <i class="fa fa-refresh"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>

                    <div class="table-container">
                        <table class="table modern-table">
                            <thead>
                                <tr>
                                    <th width="50">ID</th>
                                    <th width="260">公告信息</th>
                                    <th width="110">招聘单位</th>
                                    <th width="80">关联岗位</th>
                                    <th width="70">状态</th>
                                    <th width="110">创建时间</th>
                                    <th width="340">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <volist name="list" id="v">
                                <empty name="list">
                                    <tr>
                                        <td colspan="7" class="empty-state">
                                            <i class="fa fa-inbox"></i>
                                            <h5>暂无招聘公告</h5>
                                            <p>点击右上角"添加公告"按钮创建第一个招聘公告</p>
                                        </td>
                                    </tr>
                                </empty>
                                    <tr>
                                        <td>
                                            <span style="font-weight: 600; color: #667eea;">#{$v.id}</span>
                                        </td>
                                        <td>
                                            <div class="notice-title" title="{$v.title}">{$v.title}</div>
                                            <if condition="$v.description">
                                                <div class="notice-description" title="{$v.description}">{$v.description|mb_substr=0,35,'utf-8'}...</div>
                                            </if>
                                        </td>
                                        <td>
                                            <span style="color: #495057; font-weight: 500;">
                                                {$v.company_name|default="<span style='color: #adb5bd;'>未设置</span>"}
                                            </span>
                                        </td>
                                        <td>
                                            <span class="post-count-badge">
                                                <i class="fa fa-briefcase"></i> {$v.post_count}
                                            </span>
                                        </td>
                                        <td>
                                            <if condition="$v.status eq 1">
                                                <span class="status-badge status-active">
                                                    <i class="fa fa-check-circle"></i> 启用
                                                </span>
                                            <else />
                                                <span class="status-badge status-inactive">
                                                    <i class="fa fa-pause-circle"></i> 停用
                                                </span>
                                            </if>
                                        </td>
                                        <td>
                                            <div style="color: #6c757d; font-size: 13px;">
                                                {$v.create_time|date='Y-m-d',###}<br>
                                                <small>{$v.create_time|date='H:i:s',###}</small>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="action-buttons">
                                                <!-- 第一行按钮 -->
                                                <div style="display: flex; gap: 4px; margin-bottom: 3px;">
                                                    <a href="{:U('edit', ['id' => $v['id']])}" class="btn btn-edit" title="编辑公告">
                                                        <i class="fa fa-edit"></i> 编辑
                                                    </a>
                                                    <a href="{:U('postConfig', ['notice_id' => $v['id']])}" class="btn btn-config" title="配置岗位">
                                                        <i class="fa fa-cogs"></i> 配置
                                                    </a>
                                                    <a href="{:U('executeMatch', ['notice_id' => $v['id']])}" class="btn btn-filter" title="执行筛选" onclick="return confirm('确定要执行简历筛选吗？这可能需要一些时间。')">
                                                        <i class="fa fa-filter"></i> 筛选
                                                    </a>
                                                </div>
                                                <!-- 第二行按钮 -->
                                                <div style="display: flex; gap: 4px;">
                                                    <a href="{:U('matchResults', ['notice_id' => $v['id']])}" class="btn btn-view" title="查看结果">
                                                        <i class="fa fa-eye"></i> 结果
                                                    </a>
                                                    <if condition="$v.status eq 1">
                                                        <a href="{:U('changeStatus', ['id' => $v['id'], 'status' => 0])}" class="btn btn-toggle" title="停用" onclick="return confirm('确定要停用此公告吗？')">
                                                            <i class="fa fa-pause"></i> 停用
                                                        </a>
                                                    <else />
                                                        <a href="{:U('changeStatus', ['id' => $v['id'], 'status' => 1])}" class="btn btn-view" title="启用" onclick="return confirm('确定要启用此公告吗？')">
                                                            <i class="fa fa-play"></i> 启用
                                                        </a>
                                                    </if>
                                                    <a href="{:U('delete', ['id' => $v['id']])}" class="btn btn-delete" title="删除" onclick="return confirm('⚠️ 警告：删除后将清除所有相关数据（岗位配置、匹配结果等），此操作不可恢复！\n\n确定要删除吗？')">
                                                        <i class="fa fa-trash"></i> 删除
                                                    </a>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                </volist>
                            </tbody>
                        </table>
                    </div>

                    <if condition="$page">
                        <div class="pagination-wrapper">
                            <div class="row">
                                <div class="col-md-6">
                                    <div style="line-height: 34px; color: #6c757d;">
                                        <i class="fa fa-info-circle"></i>
                                        显示招聘公告列表，支持按标题、单位搜索和状态筛选
                                    </div>
                                </div>
                                <div class="col-md-6 text-right">
                                    {$page}
                                </div>
                            </div>
                        </div>
                    </if>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // 表格行悬停效果增强
    $('.modern-table tbody tr').hover(
        function() {
            $(this).find('.action-buttons .btn').addClass('btn-hover-effect');
        },
        function() {
            $(this).find('.action-buttons .btn').removeClass('btn-hover-effect');
        }
    );

    // 搜索表单增强
    $('.search-panel input[type="text"]').on('focus', function() {
        $(this).parent().addClass('focused');
    }).on('blur', function() {
        $(this).parent().removeClass('focused');
    });

    // 状态切换确认增强
    $('a[href*="changeStatus"]').click(function(e) {
        e.preventDefault();
        var $this = $(this);
        var isEnable = $this.attr('href').indexOf('status=1') > -1;
        var action = isEnable ? '启用' : '停用';
        var icon = isEnable ? '✅' : '⏸️';

        if (confirm(icon + ' 确定要' + action + '此招聘公告吗？\n\n' + action + '后将影响相关的筛选功能。')) {
            window.location.href = $this.attr('href');
        }
    });

    // 删除确认增强
    $('a[href*="delete"]').click(function(e) {
        e.preventDefault();
        var $this = $(this);

        if (confirm('🗑️ 危险操作警告\n\n删除招聘公告将同时删除：\n• 所有岗位配置\n• 所有筛选要求\n• 所有匹配结果\n\n此操作不可恢复，确定要继续吗？')) {
            if (confirm('⚠️ 最后确认\n\n您真的要删除这个招聘公告吗？')) {
                window.location.href = $this.attr('href');
            }
        }
    });
});
</script>

<style>
/* 分页样式优化 */
.pagination > li > a,
.pagination > li > span {
    border-radius: 4px;
    margin: 0 2px;
    border: 1px solid #ddd;
    color: #667eea;
}

.pagination > li > a:hover,
.pagination > li > span:hover {
    background-color: #667eea;
    border-color: #667eea;
    color: white;
}

.pagination > .active > a,
.pagination > .active > span {
    background-color: #667eea;
    border-color: #667eea;
}

/* 搜索框焦点效果 */
.search-panel .focused {
    transform: translateY(-1px);
    transition: all 0.3s ease;
}

/* 按钮悬停效果 */
.btn-hover-effect {
    animation: pulse 0.6s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}
</style>

<include file="block/footer" />
