<include file="block/hat" />
<div class="container-fluid">
    <div class="row">
        <include file="block/menu" />
        <div class="col-xs-12 col-sm-9 col-lg-10">
            <ul class="nav nav-tabs">
                <li class="active"><a>招聘公告管理</a></li>
            </ul>
            <style type='text/css'>.tab-pane {padding:20px 0 20px 0;}</style>
            <div class="main">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <div class="row">
                            <div class="col-md-6">
                                <h4 class="panel-title">
                                    <i class="fa fa-bullhorn"></i> 招聘公告列表
                                </h4>
                            </div>
                            <div class="col-md-6 text-right">
                                <a href="{:U('add')}" class="btn btn-primary btn-sm">
                                    <i class="fa fa-plus"></i> 添加公告
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 搜索表单 -->
                    <div class="panel-body">
                        <form class="form-inline" method="get" action="{:U('index')}">
                            <div class="form-group">
                                <select name="kw" class="form-control">
                                    <volist name="c_kw" id="v" key="k">
                                        <option value="{$k}" <if condition="$s_kw eq $k">selected</if>>{$v}</option>
                                    </volist>
                                </select>
                            </div>
                            <div class="form-group">
                                <input type="text" name="val" value="{$s_val}" class="form-control" placeholder="搜索关键词">
                            </div>
                            <div class="form-group">
                                <select name="status" class="form-control">
                                    <option value="">全部状态</option>
                                    <option value="1" <if condition="$s_status eq '1'">selected</if>>启用</option>
                                    <option value="0" <if condition="$s_status eq '0'">selected</if>>停用</option>
                                </select>
                            </div>
                            <button type="submit" class="btn btn-default">
                                <i class="fa fa-search"></i> 搜索
                            </button>
                            <a href="{:U('index')}" class="btn btn-default">
                                <i class="fa fa-refresh"></i> 重置
                            </a>
                        </form>
                    </div>

                    <div class="panel-body table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>公告标题</th>
                                    <th>招聘单位</th>
                                    <th>关联岗位</th>
                                    <th>状态</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <volist name="list" id="v" empty="<tr><td colspan='7' class='text-center text-muted'>暂无数据</td></tr>">
                                    <tr>
                                        <td>{$v.id}</td>
                                        <td>
                                            <strong>{$v.title}</strong>
                                            <if condition="$v.description">
                                                <br><small class="text-muted">{$v.description|mb_substr=0,50,'utf-8'}...</small>
                                            </if>
                                        </td>
                                        <td>{$v.company_name|default="未设置"}</td>
                                        <td>
                                            <span class="badge badge-info">{$v.post_count} 个岗位</span>
                                        </td>
                                        <td>
                                            <if condition="$v.status eq 1">
                                                <span class="label label-success">启用</span>
                                            <else />
                                                <span class="label label-default">停用</span>
                                            </if>
                                        </td>
                                        <td>{$v.create_time|date='Y-m-d H:i:s',###}</td>
                                        <td>
                                            <div class="btn-group btn-group-xs">
                                                <a href="{:U('edit', ['id' => $v['id']])}" class="btn btn-primary" title="编辑">
                                                    <i class="fa fa-edit"></i>
                                                </a>
                                                <a href="{:U('postConfig', ['notice_id' => $v['id']])}" class="btn btn-info" title="配置岗位">
                                                    <i class="fa fa-cogs"></i>
                                                </a>
                                                <a href="{:U('executeMatch', ['notice_id' => $v['id']])}" class="btn btn-warning" title="执行筛选" onclick="return confirm('确定要执行简历筛选吗？')">
                                                    <i class="fa fa-filter"></i>
                                                </a>
                                                <a href="{:U('matchResults', ['notice_id' => $v['id']])}" class="btn btn-success" title="查看结果">
                                                    <i class="fa fa-eye"></i>
                                                </a>
                                                <if condition="$v.status eq 1">
                                                    <a href="{:U('changeStatus', ['id' => $v['id'], 'status' => 0])}" class="btn btn-default" title="停用" onclick="return confirm('确定要停用吗？')">
                                                        <i class="fa fa-pause"></i>
                                                    </a>
                                                <else />
                                                    <a href="{:U('changeStatus', ['id' => $v['id'], 'status' => 1])}" class="btn btn-success" title="启用" onclick="return confirm('确定要启用吗？')">
                                                        <i class="fa fa-play"></i>
                                                    </a>
                                                </if>
                                                <a href="{:U('delete', ['id' => $v['id']])}" class="btn btn-danger" title="删除" onclick="return confirm('确定要删除吗？删除后将清除所有相关数据！')">
                                                    <i class="fa fa-trash"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                </volist>
                            </tbody>
                        </table>
                        
                        <if condition="$page">
                            <div class="text-center">
                                {$page}
                            </div>
                        </if>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<include file="block/footer" />
