<include file="block/hat" />
<div class="container-fluid">
    <div class="row">
        <include file="block/menu" />
        <div class="col-xs-12 col-sm-9 col-lg-10">
            <ul class="nav nav-tabs">
                <li class="active"><a>简历匹配详情</a></li>
            </ul>
            <style type='text/css'>.tab-pane {padding:20px 0 20px 0;}</style>
            
            <div class="main">
                <!-- 匹配概览 -->
                <div class="panel panel-<php>echo $match['is_qualified'] ? 'success' : 'warning';</php>">
                    <div class="panel-heading">
                        <h3 class="panel-title">
                            <i class="fa fa-user"></i> 匹配概览
                            <div class="pull-right">
                                <span class="label label-<php>echo $match['is_qualified'] ? 'success' : 'danger';</php>">
                                    <php>echo $match['is_qualified'] ? '符合要求' : '不符合要求';</php>
                                </span>
                                <span class="badge <php>if($match['match_score'] >= 80){</php>badge-success<php>}elseif($match['match_score'] >= 60){</php>badge-warning<php>}else{</php>badge-danger<php>}</php>">
                                    匹配分数：{$match.match_score}
                                </span>
                            </div>
                        </h3>
                    </div>
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h4>{$match.name}</h4>
                                <p class="text-muted">
                                    {$match.gender} | {$match.age|default='-'}岁 | {$match.phone}
                                </p>
                                <p>
                                    <strong>应聘岗位：</strong>{$match.job_name}<br>
                                    <strong>所属项目：</strong>{$match.project_name}<br>
                                    <strong>招聘公告：</strong>{$match.notice_title}
                                </p>
                            </div>
                            <div class="col-md-6">
                                <div class="progress" style="margin-top: 20px;">
                                    <div class="progress-bar <php>if($match['match_score'] >= 80){</php>progress-bar-success<php>}elseif($match['match_score'] >= 60){</php>progress-bar-warning<php>}else{</php>progress-bar-danger<php>}</php>" 
                                         style="width: {$match.match_score}%">
                                        {$match.match_score}分
                                    </div>
                                </div>
                                <p class="text-center" style="margin-top: 10px;">
                                    <small class="text-muted">匹配度评分</small>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 简历信息 -->
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h3 class="panel-title">
                            <i class="fa fa-file-text-o"></i> 简历信息
                        </h3>
                    </div>
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-bordered">
                                    <tr>
                                        <td width="120" class="text-right"><strong>姓名：</strong></td>
                                        <td>{$match.name}</td>
                                    </tr>
                                    <tr>
                                        <td class="text-right"><strong>性别：</strong></td>
                                        <td>{$match.gender}</td>
                                    </tr>
                                    <tr>
                                        <td class="text-right"><strong>年龄：</strong></td>
                                        <td>{$match.age|default='-'}岁</td>
                                    </tr>
                                    <tr>
                                        <td class="text-right"><strong>身高：</strong></td>
                                        <td>{$match.height|default='-'}cm</td>
                                    </tr>
                                    <tr>
                                        <td class="text-right"><strong>体重：</strong></td>
                                        <td>{$match.weight|default='-'}kg</td>
                                    </tr>
                                    <tr>
                                        <td class="text-right"><strong>婚姻状况：</strong></td>
                                        <td>{$match.marital_status|default='-'}</td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <table class="table table-bordered">
                                    <tr>
                                        <td width="120" class="text-right"><strong>学历：</strong></td>
                                        <td>{$match.education_level|default='-'}</td>
                                    </tr>
                                    <tr>
                                        <td class="text-right"><strong>专业：</strong></td>
                                        <td>{$match.major|default='-'}</td>
                                    </tr>
                                    <tr>
                                        <td class="text-right"><strong>毕业院校：</strong></td>
                                        <td>{$match.graduate_school|default='-'}</td>
                                    </tr>
                                    <tr>
                                        <td class="text-right"><strong>工作经验：</strong></td>
                                        <td>{$match.work_experience_years|default=0}年</td>
                                    </tr>
                                    <tr>
                                        <td class="text-right"><strong>政治面貌：</strong></td>
                                        <td>{$match.political_status|default='-'}</td>
                                    </tr>
                                    <tr>
                                        <td class="text-right"><strong>恐高情况：</strong></td>
                                        <td>
                                            <php>if(isset($match['is_afraid_heights'])){</php>
                                            <php>echo $match['is_afraid_heights'] ? '恐高' : '不恐高';</php>
                                            <php>}else{</php>
                                            -
                                            <php>}</php>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                        
                        <php>if($match['health_status']){</php>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="well">
                                    <h5><strong>健康状况：</strong></h5>
                                    <p>{$match.health_status}</p>
                                </div>
                            </div>
                        </div>
                        <php>}</php>
                    </div>
                </div>

                <!-- 岗位要求 -->
                <div class="panel panel-info">
                    <div class="panel-heading">
                        <h3 class="panel-title">
                            <i class="fa fa-list-alt"></i> 岗位要求
                        </h3>
                    </div>
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h5>基本要求</h5>
                                <ul class="list-unstyled">
                                    <li><strong>年龄：</strong>
                                        <php>if($requirements['min_age'] || $requirements['max_age']){</php>
                                        {$requirements.min_age|default='不限'} - {$requirements.max_age|default='不限'}岁
                                        <php>}else{</php>
                                        不限
                                        <php>}</php>
                                    </li>
                                    <li><strong>性别：</strong>
                                        <php>
                                        $genders = ['0'=>'不限','1'=>'男','2'=>'女'];
                                        echo $genders[$requirements['gender']];
                                        </php>
                                    </li>
                                    <li><strong>身高：</strong>
                                        <php>if($requirements['min_height'] || $requirements['max_height']){</php>
                                        {$requirements.min_height|default='不限'} - {$requirements.max_height|default='不限'}cm
                                        <php>}else{</php>
                                        不限
                                        <php>}</php>
                                    </li>
                                    <li><strong>婚姻状况：</strong>
                                        <php>
                                        $maritals = ['0'=>'不限','1'=>'未婚','2'=>'已婚'];
                                        echo $maritals[$requirements['marital_status']];
                                        </php>
                                    </li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h5>专业要求</h5>
                                <ul class="list-unstyled">
                                    <li><strong>学历：</strong>
                                        <php>
                                        $educations = ['0'=>'不限学历','1'=>'中专及以上','2'=>'大专及以上','3'=>'本科及以上','4'=>'研究生及以上','5'=>'硕士'];
                                        echo $educations[$requirements['education_level']];
                                        </php>
                                    </li>
                                    <li><strong>专业关键词：</strong>{$requirements.major_keywords|default='不限'}</li>
                                    <li><strong>工作经验：</strong>
                                        <php>if($requirements['min_work_years'] || $requirements['max_work_years']){</php>
                                        {$requirements.min_work_years|default='不限'} - {$requirements.max_work_years|default='不限'}年
                                        <php>}else{</php>
                                        不限
                                        <php>}</php>
                                    </li>
                                    <li><strong>恐高要求：</strong>
                                        <php>
                                        $afraidHeights = ['0'=>'不限','1'=>'不能恐高','2'=>'可以恐高'];
                                        echo $afraidHeights[$requirements['is_afraid_heights']];
                                        </php>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        
                        <php>if($requirements['health_status'] || $requirements['political_status']){</php>
                        <div class="row">
                            <div class="col-md-12">
                                <h5>其他要求</h5>
                                <ul class="list-unstyled">
                                    <php>if($requirements['health_status']){</php>
                                    <li><strong>健康状况：</strong>{$requirements.health_status}</li>
                                    <php>}</php>
                                    <php>if($requirements['political_status']){</php>
                                    <li><strong>政治面貌：</strong>{$requirements.political_status}</li>
                                    <php>}</php>
                                </ul>
                            </div>
                        </div>
                        <php>}</php>
                    </div>
                </div>

                <!-- 匹配详情 -->
                <php>if($match['match_details_array']){</php>
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h3 class="panel-title">
                            <i class="fa fa-search"></i> 详细匹配分析
                        </h3>
                    </div>
                    <div class="panel-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>匹配项目</th>
                                        <th>匹配结果</th>
                                        <th>得分</th>
                                        <th>详细说明</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <php>foreach($match['match_details_array'] as $key => $detail){</php>
                                    <tr>
                                        <td>
                                            <php>
                                            $itemNames = [
                                                'age' => '年龄',
                                                'gender' => '性别',
                                                'height' => '身高',
                                                'education' => '学历',
                                                'major' => '专业',
                                                'work_years' => '工作经验',
                                                'health' => '健康状况',
                                                'afraid_heights' => '恐高',
                                                'political' => '政治面貌',
                                                'marital' => '婚姻状况'
                                            ];
                                            echo $itemNames[$key] ?? $key;
                                            </php>
                                        </td>
                                        <td>
                                            <span class="label label-<php>echo $detail['match'] ? 'success' : 'danger';</php>">
                                                <php>echo $detail['match'] ? '匹配' : '不匹配';</php>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge <php>if($detail['score'] >= 80){</php>badge-success<php>}elseif($detail['score'] >= 60){</php>badge-warning<php>}else{</php>badge-danger<php>}</php>">
                                                {$detail.score}
                                            </span>
                                        </td>
                                        <td>{$detail.reason}</td>
                                    </tr>
                                    <php>}</php>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <php>}</php>

                <!-- 操作按钮 -->
                <div class="form-group">
                    <a href="javascript:history.back()" class="btn btn-default">
                        <i class="fa fa-arrow-left"></i> 返回
                    </a>
                    <a href="{:U('resumematch/index', array('notice_id'=>$match['notice_id']))}" class="btn btn-primary">
                        <i class="fa fa-list"></i> 返回匹配列表
                    </a>
                    <button type="button" class="btn btn-info" onclick="printDetail()">
                        <i class="fa fa-print"></i> 打印详情
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function printDetail() {
    window.print();
}
</script>

<include file="block/footer" />
