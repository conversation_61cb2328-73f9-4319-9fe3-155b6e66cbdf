<include file="block/hat" />
<div class="container-fluid">
    <div class="row">
        <include file="block/menu" />
        <div class="col-xs-12 col-sm-9 col-lg-10">
            <ul class="nav nav-tabs">
                <li class="active"><a>岗位要求配置</a></li>
            </ul>
            <style type='text/css'>.tab-pane {padding:20px 0 20px 0;}</style>
            <div class="main">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <i class="fa fa-cog"></i> 岗位要求配置
                        </h4>
                        <div class="panel-subtitle">
                            <small>招聘公告：{$notice.title} | 岗位：{$post.job_name}</small>
                        </div>
                    </div>
                    <div class="panel-body">
                        <form class="form-horizontal" method="post" action="">
                            <!-- 基本条件 -->
                            <div class="panel panel-info">
                                <div class="panel-heading">
                                    <h5><i class="fa fa-user"></i> 基本条件</h5>
                                </div>
                                <div class="panel-body">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">年龄要求</label>
                                        <div class="col-sm-4">
                                            <input type="number" name="min_age" class="form-control" 
                                                   value="{$requirements.min_age|default=''}" 
                                                   placeholder="最小年龄" min="16" max="100">
                                        </div>
                                        <div class="col-sm-4">
                                            <input type="number" name="max_age" class="form-control" 
                                                   value="{$requirements.max_age|default=''}" 
                                                   placeholder="最大年龄" min="16" max="100">
                                        </div>
                                        <div class="col-sm-2">
                                            <span class="help-block">岁</span>
                                        </div>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">性别要求</label>
                                        <div class="col-sm-8">
                                            <div class="radio-inline">
                                                <label>
                                                    <input type="radio" name="gender" value="0" 
                                                           <if condition="!isset($requirements['gender']) || $requirements['gender'] == 0">checked</if>> 不限
                                                </label>
                                            </div>
                                            <div class="radio-inline">
                                                <label>
                                                    <input type="radio" name="gender" value="1" 
                                                           <if condition="isset($requirements['gender']) && $requirements['gender'] == 1">checked</if>> 男
                                                </label>
                                            </div>
                                            <div class="radio-inline">
                                                <label>
                                                    <input type="radio" name="gender" value="2" 
                                                           <if condition="isset($requirements['gender']) && $requirements['gender'] == 2">checked</if>> 女
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">身高要求</label>
                                        <div class="col-sm-4">
                                            <input type="number" name="min_height" class="form-control" 
                                                   value="{$requirements.min_height|default=''}" 
                                                   placeholder="最小身高" min="100" max="250">
                                        </div>
                                        <div class="col-sm-4">
                                            <input type="number" name="max_height" class="form-control" 
                                                   value="{$requirements.max_height|default=''}" 
                                                   placeholder="最大身高" min="100" max="250">
                                        </div>
                                        <div class="col-sm-2">
                                            <span class="help-block">cm</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 学历要求 -->
                            <div class="panel panel-success">
                                <div class="panel-heading">
                                    <h5><i class="fa fa-graduation-cap"></i> 学历要求</h5>
                                </div>
                                <div class="panel-body">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">学历等级</label>
                                        <div class="col-sm-8">
                                            <select name="education_level" class="form-control">
                                                <option value="0" <if condition="!isset($requirements['education_level']) || $requirements['education_level'] == 0">selected</if>>不限</option>
                                                <option value="1" <if condition="isset($requirements['education_level']) && $requirements['education_level'] == 1">selected</if>>中专及以上</option>
                                                <option value="2" <if condition="isset($requirements['education_level']) && $requirements['education_level'] == 2">selected</if>>大专及以上</option>
                                                <option value="3" <if condition="isset($requirements['education_level']) && $requirements['education_level'] == 3">selected</if>>本科及以上</option>
                                                <option value="4" <if condition="isset($requirements['education_level']) && $requirements['education_level'] == 4">selected</if>>研究生及以上</option>
                                                <option value="5" <if condition="isset($requirements['education_level']) && $requirements['education_level'] == 5">selected</if>>硕士及以上</option>
                                            </select>
                                        </div>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">专业关键词</label>
                                        <div class="col-sm-8">
                                            <input type="text" name="major_keywords" class="form-control" 
                                                   value="{$requirements.major_keywords|default=''}" 
                                                   placeholder="多个关键词用逗号分隔，如：计算机,软件工程,信息技术">
                                            <span class="help-block">留空表示不限专业</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 工作经验 -->
                            <div class="panel panel-warning">
                                <div class="panel-heading">
                                    <h5><i class="fa fa-briefcase"></i> 工作经验</h5>
                                </div>
                                <div class="panel-body">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">工作年限</label>
                                        <div class="col-sm-4">
                                            <input type="number" name="min_work_years" class="form-control" 
                                                   value="{$requirements.min_work_years|default=''}" 
                                                   placeholder="最少年限" min="0" max="50">
                                        </div>
                                        <div class="col-sm-4">
                                            <input type="number" name="max_work_years" class="form-control" 
                                                   value="{$requirements.max_work_years|default=''}" 
                                                   placeholder="最多年限" min="0" max="50">
                                        </div>
                                        <div class="col-sm-2">
                                            <span class="help-block">年</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 其他要求 -->
                            <div class="panel panel-default">
                                <div class="panel-heading">
                                    <h5><i class="fa fa-list"></i> 其他要求</h5>
                                </div>
                                <div class="panel-body">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">健康状况</label>
                                        <div class="col-sm-8">
                                            <input type="text" name="health_status" class="form-control" 
                                                   value="{$requirements.health_status|default=''}" 
                                                   placeholder="如：健康，良好等">
                                        </div>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">恐高要求</label>
                                        <div class="col-sm-8">
                                            <div class="radio-inline">
                                                <label>
                                                    <input type="radio" name="is_afraid_heights" value="0" 
                                                           <if condition="!isset($requirements['is_afraid_heights']) || $requirements['is_afraid_heights'] == 0">checked</if>> 不限
                                                </label>
                                            </div>
                                            <div class="radio-inline">
                                                <label>
                                                    <input type="radio" name="is_afraid_heights" value="1" 
                                                           <if condition="isset($requirements['is_afraid_heights']) && $requirements['is_afraid_heights'] == 1">checked</if>> 不能恐高
                                                </label>
                                            </div>
                                            <div class="radio-inline">
                                                <label>
                                                    <input type="radio" name="is_afraid_heights" value="2" 
                                                           <if condition="isset($requirements['is_afraid_heights']) && $requirements['is_afraid_heights'] == 2">checked</if>> 可以恐高
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">政治面貌</label>
                                        <div class="col-sm-8">
                                            <input type="text" name="political_status" class="form-control" 
                                                   value="{$requirements.political_status|default=''}" 
                                                   placeholder="如：党员，团员等，留空表示不限">
                                        </div>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">婚姻状况</label>
                                        <div class="col-sm-8">
                                            <div class="radio-inline">
                                                <label>
                                                    <input type="radio" name="marital_status" value="0" 
                                                           <if condition="!isset($requirements['marital_status']) || $requirements['marital_status'] == 0">checked</if>> 不限
                                                </label>
                                            </div>
                                            <div class="radio-inline">
                                                <label>
                                                    <input type="radio" name="marital_status" value="1" 
                                                           <if condition="isset($requirements['marital_status']) && $requirements['marital_status'] == 1">checked</if>> 未婚
                                                </label>
                                            </div>
                                            <div class="radio-inline">
                                                <label>
                                                    <input type="radio" name="marital_status" value="2" 
                                                           <if condition="isset($requirements['marital_status']) && $requirements['marital_status'] == 2">checked</if>> 已婚
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <div class="col-sm-offset-2 col-sm-8">
                                    <button type="submit" class="btn btn-primary" style="padding: 12px 24px; font-size: 14px; font-weight: 600;">
                                        <i class="fa fa-save"></i> 保存要求
                                    </button>
                                    <a href="{:U('postConfig', ['notice_id' => $notice['id']])}" class="btn btn-default" style="padding: 12px 24px; font-size: 14px; font-weight: 600; margin-left: 10px;">
                                        <i class="fa fa-arrow-left"></i> 返回岗位配置
                                    </a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<include file="block/footer" />
