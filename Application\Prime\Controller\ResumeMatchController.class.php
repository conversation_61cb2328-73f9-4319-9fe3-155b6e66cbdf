<?php
namespace Prime\Controller;

use Common\Controller\PrimeController;

/**
 * 简历匹配控制器
 */
class ResumeMatchController extends PrimeController
{
    public function _initialize()
    {
        parent::_initialize();
    }

    /**
     * 安全的分页处理
     * @param int $count 总记录数
     * @param int $listRows 每页记录数
     * @return object 分页对象
     */
    protected function safePage($count, $listRows = 20)
    {
        $count = max(0, intval($count));
        $listRows = max(1, intval($listRows));

        $page = $this->page($count, $listRows);

        // 确保当前页码有效
        if (!isset($page->nowPage) || $page->nowPage < 1) {
            $page->nowPage = 1;
        } else {
            $page->nowPage = max(1, intval($page->nowPage));
            $maxPage = $count > 0 ? ceil($count / $listRows) : 1;
            $page->nowPage = min($page->nowPage, $maxPage);
        }

        return $page;
    }

    /**
     * 匹配结果列表
     */
    public function index()
    {
        $notice_id = intval(I('get.notice_id'));
        if (!$notice_id) {
            $this->error('参数错误');
        }
        
        // 获取招聘公告信息
        $notice = D('RecruitmentNotice')->where(['id' => $notice_id])->find();
        if (!$notice) {
            $this->error('招聘公告不存在');
        }
        
        // 搜索条件
        $filters = [
            'is_qualified' => I('get.is_qualified', ''),
            'min_score' => I('get.min_score', ''),
            'max_score' => I('get.max_score', ''),
            'post_id' => intval(I('get.post_id', 0))
        ];
        
        $obj = D('ResumePostMatch');
        
        // 获取总数
        $where = ['rpm.notice_id' => $notice_id];
        if ($filters['is_qualified'] !== '') {
            $where['rpm.is_qualified'] = $filters['is_qualified'];
        }
        if ($filters['min_score'] !== '') {
            $where['rpm.match_score'] = ['egt', $filters['min_score']];
        }
        if ($filters['max_score'] !== '') {
            if (isset($where['rpm.match_score'])) {
                $where['rpm.match_score'] = [
                    ['egt', $filters['min_score']],
                    ['elt', $filters['max_score']]
                ];
            } else {
                $where['rpm.match_score'] = ['elt', $filters['max_score']];
            }
        }
        if ($filters['post_id'] > 0) {
            $where['rpm.post_id'] = $filters['post_id'];
        }
        
        $count = $obj->alias('rpm')->where($where)->count();
        $page = $this->safePage($count, 20);

        $list = $obj->getNoticeMatches($notice_id, $filters, $page->nowPage, 20);
        
        // 获取公告的岗位列表用于筛选
        $posts = D('RecruitmentNotice')->getNoticePosts($notice_id);
        
        // 获取匹配统计
        $stats = $obj->getMatchStats($notice_id);
        
        $this->assign('notice', $notice);
        $this->assign('list', $list);
        $this->assign('page', $page->show());
        $this->assign('filters', $filters);
        $this->assign('posts', $posts);
        $this->assign('stats', $stats);
        $this->assign('qualified_options', $obj->is_qualified);
        
        $this->display();
    }

    /**
     * 执行简历匹配
     */
    public function do_match()
    {
        $notice_id = intval(I('post.notice_id'));
        if (!$notice_id) {
            $this->error('参数错误');
        }
        
        // 获取招聘公告信息
        $notice = D('RecruitmentNotice')->where(['id' => $notice_id])->find();
        if (!$notice) {
            $this->error('招聘公告不存在');
        }
        
        // 获取公告的岗位要求
        $requirements = D('PostRequirements')->getNoticeRequirements($notice_id);
        if (empty($requirements)) {
            $this->error('该招聘公告下没有配置岗位要求');
        }
        
        // 清除旧的匹配记录
        D('ResumePostMatch')->deleteMatches($notice_id);
        
        $matchService = new \Common\Service\ResumeMatchService();
        $totalMatched = 0;
        
        foreach ($requirements as $requirement) {
            // 执行匹配
            $matches = $matchService->matchResumes($requirement);
            
            if (!empty($matches)) {
                // 批量保存匹配结果
                D('ResumePostMatch')->batchSaveMatches($matches);
                $totalMatched += count($matches);
            }
        }
        
        $this->success("匹配完成，共匹配到 {$totalMatched} 条记录", U('resumematch/index', ['notice_id' => $notice_id]));
    }

    /**
     * 查看匹配详情
     */
    public function detail()
    {
        $id = intval(I('get.id'));
        if (!$id) {
            $this->error('参数错误');
        }
        
        // 获取匹配记录
        $match = D('ResumePostMatch')->alias('rpm')
            ->join('LEFT JOIN __USER_JOB__ uj ON rpm.user_job_id = uj.id')
            ->join('LEFT JOIN __PROJECT_POST__ pp ON rpm.post_id = pp.id')
            ->join('LEFT JOIN __PROJECT__ p ON pp.project_id = p.id')
            ->join('LEFT JOIN __RECRUITMENT_NOTICE__ rn ON rpm.notice_id = rn.id')
            ->where(['rpm.id' => $id])
            ->field('rpm.*, uj.*, pp.job_name, p.name as project_name, rn.title as notice_title')
            ->find();
            
        if (!$match) {
            $this->error('匹配记录不存在');
        }
        
        // 解析匹配详情
        $match['match_details_array'] = json_decode($match['match_details'], true);
        
        // 获取岗位要求
        $requirements = D('PostRequirements')->getRequirements($match['post_id'], $match['notice_id']);
        
        // 计算年龄
        if ($match['birthdate']) {
            $match['age'] = date('Y') - date('Y', strtotime($match['birthdate']));
        }
        
        $this->assign('match', $match);
        $this->assign('requirements', $requirements);
        
        $this->display();
    }

    /**
     * 岗位匹配结果
     */
    public function post_matches()
    {
        $notice_id = intval(I('get.notice_id'));
        $post_id = intval(I('get.post_id'));
        
        if (!$notice_id || !$post_id) {
            $this->error('参数错误');
        }
        
        // 获取招聘公告信息
        $notice = D('RecruitmentNotice')->where(['id' => $notice_id])->find();
        if (!$notice) {
            $this->error('招聘公告不存在');
        }
        
        // 获取岗位信息
        $post = D('ProjectPost')->alias('pp')
            ->join('LEFT JOIN __PROJECT__ p ON pp.project_id = p.id')
            ->where(['pp.id' => $post_id])
            ->field('pp.*, p.name as project_name')
            ->find();
        if (!$post) {
            $this->error('岗位不存在');
        }
        
        // 搜索条件
        $filters = [
            'is_qualified' => I('get.is_qualified', ''),
            'min_score' => I('get.min_score', ''),
            'max_score' => I('get.max_score', '')
        ];
        
        $obj = D('ResumePostMatch');
        
        // 获取总数
        $where = ['rpm.notice_id' => $notice_id, 'rpm.post_id' => $post_id];
        if ($filters['is_qualified'] !== '') {
            $where['rpm.is_qualified'] = $filters['is_qualified'];
        }
        if ($filters['min_score'] !== '') {
            $where['rpm.match_score'] = ['egt', $filters['min_score']];
        }
        if ($filters['max_score'] !== '') {
            if (isset($where['rpm.match_score'])) {
                $where['rpm.match_score'] = [
                    ['egt', $filters['min_score']],
                    ['elt', $filters['max_score']]
                ];
            } else {
                $where['rpm.match_score'] = ['elt', $filters['max_score']];
            }
        }
        
        $count = $obj->alias('rpm')->where($where)->count();
        $page = $this->safePage($count, 20);

        $list = $obj->getPostMatches($post_id, $notice_id, $filters, $page->nowPage, 20);
        
        // 获取岗位要求
        $requirements = D('PostRequirements')->getRequirements($post_id, $notice_id);
        
        $this->assign('notice', $notice);
        $this->assign('post', $post);
        $this->assign('requirements', $requirements);
        $this->assign('list', $list);
        $this->assign('page', $page->show());
        $this->assign('filters', $filters);
        $this->assign('qualified_options', $obj->is_qualified);
        
        $this->display();
    }

    /**
     * 导出匹配结果
     */
    public function export()
    {
        $notice_id = intval(I('get.notice_id'));
        if (!$notice_id) {
            $this->error('参数错误');
        }
        
        // 获取招聘公告信息
        $notice = D('RecruitmentNotice')->where(['id' => $notice_id])->find();
        if (!$notice) {
            $this->error('招聘公告不存在');
        }
        
        // 获取所有匹配结果
        $matches = D('ResumePostMatch')->alias('rpm')
            ->join('LEFT JOIN __USER_JOB__ uj ON rpm.user_job_id = uj.id')
            ->join('LEFT JOIN __PROJECT_POST__ pp ON rpm.post_id = pp.id')
            ->join('LEFT JOIN __PROJECT__ p ON pp.project_id = p.id')
            ->where(['rpm.notice_id' => $notice_id])
            ->field('rpm.*, uj.name, uj.gender, uj.birthdate, uj.phone, uj.education_level, uj.major, uj.height, pp.job_name, p.name as project_name')
            ->order('rpm.match_score DESC')
            ->select();
        
        // 生成Excel文件
        vendor('PHPExcel.PHPExcel');
        $objPHPExcel = new \PHPExcel();
        
        // 设置表头
        $objPHPExcel->getActiveSheet()->setCellValue('A1', '姓名');
        $objPHPExcel->getActiveSheet()->setCellValue('B1', '性别');
        $objPHPExcel->getActiveSheet()->setCellValue('C1', '年龄');
        $objPHPExcel->getActiveSheet()->setCellValue('D1', '电话');
        $objPHPExcel->getActiveSheet()->setCellValue('E1', '学历');
        $objPHPExcel->getActiveSheet()->setCellValue('F1', '专业');
        $objPHPExcel->getActiveSheet()->setCellValue('G1', '身高');
        $objPHPExcel->getActiveSheet()->setCellValue('H1', '岗位');
        $objPHPExcel->getActiveSheet()->setCellValue('I1', '项目');
        $objPHPExcel->getActiveSheet()->setCellValue('J1', '匹配分数');
        $objPHPExcel->getActiveSheet()->setCellValue('K1', '是否符合');
        
        // 填充数据
        $row = 2;
        foreach ($matches as $match) {
            $age = $match['birthdate'] ? (date('Y') - date('Y', strtotime($match['birthdate']))) : '';
            $isQualified = $match['is_qualified'] ? '符合' : '不符合';
            
            $objPHPExcel->getActiveSheet()->setCellValue('A' . $row, $match['name']);
            $objPHPExcel->getActiveSheet()->setCellValue('B' . $row, $match['gender']);
            $objPHPExcel->getActiveSheet()->setCellValue('C' . $row, $age);
            $objPHPExcel->getActiveSheet()->setCellValue('D' . $row, $match['phone']);
            $objPHPExcel->getActiveSheet()->setCellValue('E' . $row, $match['education_level']);
            $objPHPExcel->getActiveSheet()->setCellValue('F' . $row, $match['major']);
            $objPHPExcel->getActiveSheet()->setCellValue('G' . $row, $match['height']);
            $objPHPExcel->getActiveSheet()->setCellValue('H' . $row, $match['job_name']);
            $objPHPExcel->getActiveSheet()->setCellValue('I' . $row, $match['project_name']);
            $objPHPExcel->getActiveSheet()->setCellValue('J' . $row, $match['match_score']);
            $objPHPExcel->getActiveSheet()->setCellValue('K' . $row, $isQualified);
            
            $row++;
        }
        
        // 输出Excel文件
        $filename = $notice['title'] . '_匹配结果_' . date('YmdHis') . '.xlsx';
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="' . $filename . '"');
        header('Cache-Control: max-age=0');
        
        $objWriter = \PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel2007');
        $objWriter->save('php://output');
        exit;
    }
}
