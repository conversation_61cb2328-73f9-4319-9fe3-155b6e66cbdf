<include file="block/hat" />
<div class="container-fluid">
    <div class="row">
        <include file="block/menu" />
        <div class="col-xs-12 col-sm-9 col-lg-10">
            <ul class="nav nav-tabs">
                <li class="active"><a>配置岗位要求</a></li>
            </ul>
            <style type='text/css'>.tab-pane {padding:20px 0 20px 0;}</style>
            
            <div class="main">
                <!-- 岗位信息 -->
                <div class="panel panel-info">
                    <div class="panel-heading">
                        <h3 class="panel-title">
                            <i class="fa fa-info-circle"></i> 岗位信息
                        </h3>
                    </div>
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h4>{$post.job_name}</h4>
                                <p class="text-muted">所属项目：{$post.project_name}</p>
                                <p class="text-muted">服务价格：{$post.service_price}</p>
                            </div>
                            <div class="col-md-6">
                                <h5>招聘公告：{$notice.title}</h5>
                                <p class="text-muted">招聘单位：{$notice.company_name}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 要求配置表单 -->
                <form action="{:U('postrequirements/edit')}" method="post" class="form-horizontal js-ajax-form">
                    <input type="hidden" name="notice_id" value="{$notice.id}" />
                    <input type="hidden" name="post_id" value="{$post.id}" />
                    
                    <!-- 基本条件 -->
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h3 class="panel-title">
                                <i class="fa fa-user"></i> 基本条件
                            </h3>
                        </div>
                        <div class="panel-body">
                            <!-- 年龄要求 -->
                            <div class="form-group">
                                <label class="col-sm-2 control-label">年龄要求</label>
                                <div class="col-sm-8">
                                    <div class="row">
                                        <div class="col-sm-4">
                                            <div class="input-group">
                                                <input type="number" class="form-control" name="min_age" value="{$requirements.min_age|default=''}" placeholder="最小年龄" min="16" max="65">
                                                <span class="input-group-addon">岁</span>
                                            </div>
                                        </div>
                                        <div class="col-sm-1 text-center" style="padding-top: 7px;">至</div>
                                        <div class="col-sm-4">
                                            <div class="input-group">
                                                <input type="number" class="form-control" name="max_age" value="{$requirements.max_age|default=''}" placeholder="最大年龄" min="16" max="65">
                                                <span class="input-group-addon">岁</span>
                                            </div>
                                        </div>
                                    </div>
                                    <span class="help-block">留空表示不限制，年龄范围：16-65岁</span>
                                </div>
                            </div>

                            <!-- 性别要求 -->
                            <div class="form-group">
                                <label class="col-sm-2 control-label">性别要求</label>
                                <div class="col-sm-8">
                                    <php>foreach($gender_options as $key=>$value){</php>
                                    <label class="radio-inline">
                                        <input type="radio" name="gender" value="{$key}" <php>if((isset($requirements) && $requirements['gender'] == $key) || (!isset($requirements) && $key == 0)){</php>checked<php>}</php>>
                                        {$value.text}
                                    </label>
                                    <php>}</php>
                                </div>
                            </div>

                            <!-- 身高要求 -->
                            <div class="form-group">
                                <label class="col-sm-2 control-label">身高要求</label>
                                <div class="col-sm-8">
                                    <div class="row">
                                        <div class="col-sm-4">
                                            <div class="input-group">
                                                <input type="number" class="form-control" name="min_height" value="{$requirements.min_height|default=''}" placeholder="最小身高" min="140" max="220">
                                                <span class="input-group-addon">cm</span>
                                            </div>
                                        </div>
                                        <div class="col-sm-1 text-center" style="padding-top: 7px;">至</div>
                                        <div class="col-sm-4">
                                            <div class="input-group">
                                                <input type="number" class="form-control" name="max_height" value="{$requirements.max_height|default=''}" placeholder="最大身高" min="140" max="220">
                                                <span class="input-group-addon">cm</span>
                                            </div>
                                        </div>
                                    </div>
                                    <span class="help-block">留空表示不限制，身高范围：140-220cm</span>
                                </div>
                            </div>

                            <!-- 婚姻状况 -->
                            <div class="form-group">
                                <label class="col-sm-2 control-label">婚姻状况</label>
                                <div class="col-sm-8">
                                    <php>foreach($marital_options as $key=>$value){</php>
                                    <label class="radio-inline">
                                        <input type="radio" name="marital_status" value="{$key}" <php>if((isset($requirements) && $requirements['marital_status'] == $key) || (!isset($requirements) && $key == 0)){</php>checked<php>}</php>>
                                        {$value.text}
                                    </label>
                                    <php>}</php>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 学历和专业要求 -->
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h3 class="panel-title">
                                <i class="fa fa-graduation-cap"></i> 学历和专业要求
                            </h3>
                        </div>
                        <div class="panel-body">
                            <!-- 学历要求 -->
                            <div class="form-group">
                                <label class="col-sm-2 control-label">学历要求</label>
                                <div class="col-sm-8">
                                    <php>foreach($education_options as $key=>$value){</php>
                                    <label class="radio-inline">
                                        <input type="radio" name="education_level" value="{$key}" <php>if((isset($requirements) && $requirements['education_level'] == $key) || (!isset($requirements) && $key == 0)){</php>checked<php>}</php>>
                                        {$value.text}
                                    </label>
                                    <php>}</php>
                                </div>
                            </div>

                            <!-- 专业关键词 -->
                            <div class="form-group">
                                <label class="col-sm-2 control-label">专业关键词</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" name="major_keywords" value="{$requirements.major_keywords|default=''}" placeholder="请输入专业关键词，多个关键词用逗号分隔">
                                    <span class="help-block">例如：计算机,软件工程,信息技术。留空表示不限专业</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 工作经验要求 -->
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h3 class="panel-title">
                                <i class="fa fa-briefcase"></i> 工作经验要求
                            </h3>
                        </div>
                        <div class="panel-body">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">工作年限</label>
                                <div class="col-sm-8">
                                    <div class="row">
                                        <div class="col-sm-4">
                                            <div class="input-group">
                                                <input type="number" class="form-control" name="min_work_years" value="{$requirements.min_work_years|default=''}" placeholder="最少年限" min="0" max="50">
                                                <span class="input-group-addon">年</span>
                                            </div>
                                        </div>
                                        <div class="col-sm-1 text-center" style="padding-top: 7px;">至</div>
                                        <div class="col-sm-4">
                                            <div class="input-group">
                                                <input type="number" class="form-control" name="max_work_years" value="{$requirements.max_work_years|default=''}" placeholder="最多年限" min="0" max="50">
                                                <span class="input-group-addon">年</span>
                                            </div>
                                        </div>
                                    </div>
                                    <span class="help-block">留空表示不限制，工作年限范围：0-50年</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 其他要求 -->
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h3 class="panel-title">
                                <i class="fa fa-list-alt"></i> 其他要求
                            </h3>
                        </div>
                        <div class="panel-body">
                            <!-- 健康状况 -->
                            <div class="form-group">
                                <label class="col-sm-2 control-label">健康状况</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" name="health_status" value="{$requirements.health_status|default=''}" placeholder="请输入健康状况要求">
                                    <span class="help-block">例如：身体健康，无传染性疾病。留空表示不限制</span>
                                </div>
                            </div>

                            <!-- 恐高要求 -->
                            <div class="form-group">
                                <label class="col-sm-2 control-label">恐高要求</label>
                                <div class="col-sm-8">
                                    <php>foreach($afraid_heights_options as $key=>$value){</php>
                                    <label class="radio-inline">
                                        <input type="radio" name="is_afraid_heights" value="{$key}" <php>if((isset($requirements) && $requirements['is_afraid_heights'] == $key) || (!isset($requirements) && $key == 0)){</php>checked<php>}</php>>
                                        {$value.text}
                                    </label>
                                    <php>}</php>
                                </div>
                            </div>

                            <!-- 政治面貌 -->
                            <div class="form-group">
                                <label class="col-sm-2 control-label">政治面貌</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" name="political_status" value="{$requirements.political_status|default=''}" placeholder="请输入政治面貌要求">
                                    <span class="help-block">例如：中共党员，共青团员。留空表示不限制</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="form-group">
                        <div class="col-sm-offset-2 col-sm-8">
                            <button type="submit" class="btn btn-primary">
                                <i class="fa fa-save"></i> 保存配置
                            </button>
                            <a href="{:U('postrequirements/index', array('notice_id'=>$notice['id']))}" class="btn btn-default">
                                <i class="fa fa-arrow-left"></i> 返回列表
                            </a>
                            <button type="button" class="btn btn-info" onclick="previewFilter()">
                                <i class="fa fa-eye"></i> 预览筛选
                            </button>
                            <button type="button" class="btn btn-warning" onclick="resetForm()">
                                <i class="fa fa-refresh"></i> 重置
                            </button>
                        </div>
                    </div>
                </form>

                <!-- 配置提示 -->
                <div class="alert alert-info">
                    <h4><i class="fa fa-lightbulb-o"></i> 配置说明</h4>
                    <ul class="mb-0">
                        <li>所有条件都是可选的，留空表示不限制该项条件</li>
                        <li>年龄和身高支持范围设置，可以只设置最小值或最大值</li>
                        <li>专业关键词支持模糊匹配，多个关键词用逗号分隔</li>
                        <li>配置完成后建议先预览筛选结果，确认无误后再保存</li>
                        <li>保存后可以在招聘公告详情页执行完整的简历匹配</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(function(){
    // 表单验证
    $('.js-ajax-form').on('submit', function(e) {
        var minAge = parseInt($('input[name="min_age"]').val()) || 0;
        var maxAge = parseInt($('input[name="max_age"]').val()) || 0;
        var minHeight = parseInt($('input[name="min_height"]').val()) || 0;
        var maxHeight = parseInt($('input[name="max_height"]').val()) || 0;
        var minWorkYears = parseInt($('input[name="min_work_years"]').val()) || 0;
        var maxWorkYears = parseInt($('input[name="max_work_years"]').val()) || 0;

        // 验证年龄范围
        if (minAge > 0 && maxAge > 0 && minAge >= maxAge) {
            layer.msg('最小年龄必须小于最大年龄', {icon: 2});
            return false;
        }

        // 验证身高范围
        if (minHeight > 0 && maxHeight > 0 && minHeight >= maxHeight) {
            layer.msg('最小身高必须小于最大身高', {icon: 2});
            return false;
        }

        // 验证工作年限范围
        if (minWorkYears > 0 && maxWorkYears > 0 && minWorkYears >= maxWorkYears) {
            layer.msg('最少工作年限必须小于最多工作年限', {icon: 2});
            return false;
        }

        // 显示保存状态
        var $submitBtn = $(this).find('button[type="submit"]');
        var originalText = $submitBtn.html();
        $submitBtn.html('<i class="fa fa-spinner fa-spin"></i> 保存中...');
        $submitBtn.prop('disabled', true);

        // 恢复按钮状态（如果提交失败）
        setTimeout(function() {
            $submitBtn.html(originalText);
            $submitBtn.prop('disabled', false);
        }, 3000);
    });
});

// 预览筛选
function previewFilter() {
    var noticeId = $('input[name="notice_id"]').val();
    var postId = $('input[name="post_id"]').val();
    var url = "{:U('postrequirements/preview')}" + "?notice_id=" + noticeId + "&post_id=" + postId;
    window.open(url, '_blank');
}

// 重置表单
function resetForm() {
    if (confirm('确定要重置所有配置吗？')) {
        $('input[type="number"]').val('');
        $('input[type="text"]').val('');
        $('input[type="radio"][value="0"]').prop('checked', true);
    }
}
</script>

<include file="block/footer" />
