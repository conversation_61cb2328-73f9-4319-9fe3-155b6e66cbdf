<?php
namespace Common\Model;

use Think\Model;

/**
 * 简历岗位匹配模型
 */
class ResumePostMatchModel extends Model
{
    protected $_validate = [
        ['user_job_id', 'require', '简历ID不能为空'],
        ['post_id', 'require', '岗位ID不能为空'],
        ['notice_id', 'require', '招聘公告ID不能为空'],
        ['match_score', 'between:0,100', '匹配分数范围不正确', 0, 'regex'],
        ['is_qualified', 'in:0,1', '是否符合要求值不正确', 0, 'regex'],
    ];

    protected $_auto = [
        ['create_time', 'time', self::MODEL_INSERT, 'function'],
        ['update_time', 'time', self::MODEL_BOTH, 'function'],
    ];

    /**
     * 匹配等级定义
     */
    public $matchLevels = [
        'excellent' => ['min' => 90, 'max' => 100, 'text' => '优秀匹配', 'class' => 'success'],
        'good' => ['min' => 80, 'max' => 89, 'text' => '良好匹配', 'class' => 'info'],
        'fair' => ['min' => 70, 'max' => 79, 'text' => '一般匹配', 'class' => 'warning'],
        'poor' => ['min' => 0, 'max' => 69, 'text' => '匹配度低', 'class' => 'danger']
    ];

    /**
     * 获取匹配等级
     */
    public function getMatchLevel($score)
    {
        foreach ($this->matchLevels as $level => $config) {
            if ($score >= $config['min'] && $score <= $config['max']) {
                return [
                    'level' => $level,
                    'text' => $config['text'],
                    'class' => $config['class']
                ];
            }
        }
        return [
            'level' => 'poor',
            'text' => '匹配度低',
            'class' => 'danger'
        ];
    }

    /**
     * 获取匹配统计
     */
    public function getMatchStats($notice_id, $post_id = null)
    {
        $where = ['notice_id' => $notice_id];
        if ($post_id) {
            $where['post_id'] = $post_id;
        }

        $stats = [];
        
        // 总匹配数
        $stats['total'] = $this->where($where)->count();
        
        // 符合条件数
        $stats['qualified'] = $this->where(array_merge($where, ['is_qualified' => 1]))->count();
        
        // 不符合条件数
        $stats['unqualified'] = $stats['total'] - $stats['qualified'];
        
        // 各等级匹配数
        foreach ($this->matchLevels as $level => $config) {
            $levelWhere = array_merge($where, [
                'match_score' => ['between', [$config['min'], $config['max']]]
            ]);
            $stats[$level] = $this->where($levelWhere)->count();
        }
        
        // 平均分数
        $avgScore = $this->where($where)->avg('match_score');
        $stats['avg_score'] = $avgScore ? round($avgScore, 2) : 0;
        
        return $stats;
    }

    /**
     * 获取最佳匹配简历
     */
    public function getBestMatches($notice_id, $post_id = null, $limit = 10)
    {
        $where = ['m.notice_id' => $notice_id, 'm.is_qualified' => 1];
        if ($post_id) {
            $where['m.post_id'] = $post_id;
        }

        return $this->alias('m')
            ->join('LEFT JOIN __USER_JOB__ uj ON m.user_job_id = uj.id')
            ->join('LEFT JOIN __PROJECT_POST__ pp ON m.post_id = pp.id')
            ->join('LEFT JOIN __PROJECT__ p ON pp.project_id = p.id')
            ->where($where)
            ->field('m.*, uj.name, uj.gender, uj.phone, uj.education_level, pp.job_name, p.name as project_name')
            ->order('m.match_score DESC')
            ->limit($limit)
            ->select();
    }

    /**
     * 获取简历的所有匹配记录
     */
    public function getResumeMatches($user_job_id)
    {
        return $this->alias('m')
            ->join('LEFT JOIN __PROJECT_POST__ pp ON m.post_id = pp.id')
            ->join('LEFT JOIN __PROJECT__ p ON pp.project_id = p.id')
            ->join('LEFT JOIN __RECRUITMENT_NOTICE__ rn ON m.notice_id = rn.id')
            ->where(['m.user_job_id' => $user_job_id])
            ->field('m.*, pp.job_name, p.name as project_name, rn.title as notice_title')
            ->order('m.match_score DESC')
            ->select();
    }

    /**
     * 批量删除匹配记录
     */
    public function deleteByNotice($notice_id)
    {
        return $this->where(['notice_id' => $notice_id])->delete();
    }

    /**
     * 批量删除岗位匹配记录
     */
    public function deleteByPost($post_id)
    {
        return $this->where(['post_id' => $post_id])->delete();
    }

    /**
     * 更新匹配记录
     */
    public function updateMatch($user_job_id, $post_id, $notice_id, $matchData)
    {
        $where = [
            'user_job_id' => $user_job_id,
            'post_id' => $post_id,
            'notice_id' => $notice_id
        ];
        
        $existing = $this->where($where)->find();
        
        $data = [
            'match_score' => $matchData['score'],
            'match_details' => json_encode($matchData['details']),
            'is_qualified' => $matchData['qualified'] ? 1 : 0,
            'update_time' => time()
        ];
        
        if ($existing) {
            $data['id'] = $existing['id'];
            return $this->save($data);
        } else {
            $data = array_merge($where, $data);
            $data['create_time'] = time();
            return $this->add($data);
        }
    }
}
