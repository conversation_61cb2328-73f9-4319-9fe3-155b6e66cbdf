<?php
namespace Common\Model;

use Think\Model;

/**
 * 岗位筛选要求模型
 */
class PostRequirementsModel extends Model
{
    protected $_validate = [
        ['post_id', 'require', '岗位ID不能为空'],
        ['notice_id', 'require', '招聘公告ID不能为空'],
        ['min_age', 'between:16,100', '最小年龄范围不正确', 0, 'regex'],
        ['max_age', 'between:16,100', '最大年龄范围不正确', 0, 'regex'],
        ['gender', 'in:0,1,2', '性别要求不正确', 0, 'regex'],
        ['min_height', 'between:100,250', '最小身高范围不正确', 0, 'regex'],
        ['max_height', 'between:100,250', '最大身高范围不正确', 0, 'regex'],
        ['education_level', 'in:0,1,2,3,4,5', '学历要求不正确', 0, 'regex'],
        ['is_afraid_heights', 'in:0,1,2', '恐高要求不正确', 0, 'regex'],
        ['marital_status', 'in:0,1,2', '婚姻状况要求不正确', 0, 'regex'],
    ];

    protected $_auto = [
        ['create_time', 'time', self::MODEL_INSERT, 'function'],
        ['update_time', 'time', self::MODEL_BOTH, 'function'],
    ];

    /**
     * 性别选项
     */
    public $genderOptions = [
        0 => '不限',
        1 => '男',
        2 => '女'
    ];

    /**
     * 学历等级选项
     */
    public $educationOptions = [
        0 => '不限',
        1 => '中专及以上',
        2 => '大专及以上',
        3 => '本科及以上',
        4 => '研究生及以上',
        5 => '硕士及以上'
    ];

    /**
     * 恐高要求选项
     */
    public $afraidHeightsOptions = [
        0 => '不限',
        1 => '不能恐高',
        2 => '可以恐高'
    ];

    /**
     * 婚姻状况选项
     */
    public $maritalStatusOptions = [
        0 => '不限',
        1 => '未婚',
        2 => '已婚'
    ];

    /**
     * 获取性别文本
     */
    public function getGenderText($gender)
    {
        return isset($this->genderOptions[$gender]) ? $this->genderOptions[$gender] : '不限';
    }

    /**
     * 获取学历文本
     */
    public function getEducationText($education)
    {
        return isset($this->educationOptions[$education]) ? $this->educationOptions[$education] : '不限';
    }

    /**
     * 获取恐高要求文本
     */
    public function getAfraidHeightsText($afraid)
    {
        return isset($this->afraidHeightsOptions[$afraid]) ? $this->afraidHeightsOptions[$afraid] : '不限';
    }

    /**
     * 获取婚姻状况文本
     */
    public function getMaritalStatusText($status)
    {
        return isset($this->maritalStatusOptions[$status]) ? $this->maritalStatusOptions[$status] : '不限';
    }

    /**
     * 验证年龄范围
     */
    public function validateAgeRange($data)
    {
        if (!empty($data['min_age']) && !empty($data['max_age'])) {
            if ($data['min_age'] > $data['max_age']) {
                return '最小年龄不能大于最大年龄';
            }
        }
        return true;
    }

    /**
     * 验证身高范围
     */
    public function validateHeightRange($data)
    {
        if (!empty($data['min_height']) && !empty($data['max_height'])) {
            if ($data['min_height'] > $data['max_height']) {
                return '最小身高不能大于最大身高';
            }
        }
        return true;
    }

    /**
     * 验证工作年限范围
     */
    public function validateWorkYearsRange($data)
    {
        if (!empty($data['min_work_years']) && !empty($data['max_work_years'])) {
            if ($data['min_work_years'] > $data['max_work_years']) {
                return '最少工作年限不能大于最多工作年限';
            }
        }
        return true;
    }

    /**
     * 获取要求摘要
     */
    public function getRequirementsSummary($requirements)
    {
        $summary = [];
        
        if (!empty($requirements['min_age']) || !empty($requirements['max_age'])) {
            $ageRange = '';
            if (!empty($requirements['min_age'])) {
                $ageRange .= $requirements['min_age'] . '岁';
            }
            if (!empty($requirements['max_age'])) {
                $ageRange .= ($ageRange ? '-' : '') . $requirements['max_age'] . '岁';
            }
            $summary[] = '年龄：' . $ageRange;
        }
        
        if (!empty($requirements['gender']) && $requirements['gender'] != 0) {
            $summary[] = '性别：' . $this->getGenderText($requirements['gender']);
        }
        
        if (!empty($requirements['min_height']) || !empty($requirements['max_height'])) {
            $heightRange = '';
            if (!empty($requirements['min_height'])) {
                $heightRange .= $requirements['min_height'] . 'cm';
            }
            if (!empty($requirements['max_height'])) {
                $heightRange .= ($heightRange ? '-' : '') . $requirements['max_height'] . 'cm';
            }
            $summary[] = '身高：' . $heightRange;
        }
        
        if (!empty($requirements['education_level']) && $requirements['education_level'] != 0) {
            $summary[] = '学历：' . $this->getEducationText($requirements['education_level']);
        }
        
        return implode('；', $summary);
    }
}
