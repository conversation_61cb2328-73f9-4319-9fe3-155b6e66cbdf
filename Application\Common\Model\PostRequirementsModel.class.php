<?php
namespace Common\Model;

use Common\Model\CommonModel;

/**
 * 岗位筛选要求模型
 */
class PostRequirementsModel extends CommonModel
{
    protected $_auto = [
        ['create_time', 'time', self::MODEL_INSERT, 'function'],
        ['update_time', 'time', self::MODEL_UPDATE, 'function'],
    ];

    /**
     * 性别要求定义
     */
    public $gender = [
        '0' => ['text' => '不限', 'style' => 'info'],
        '1' => ['text' => '男', 'style' => 'primary'],
        '2' => ['text' => '女', 'style' => 'warning'],
    ];

    /**
     * 学历要求定义
     */
    public $education_level = [
        '0' => ['text' => '不限学历', 'style' => 'info'],
        '1' => ['text' => '中专及以上', 'style' => 'info'],
        '2' => ['text' => '大专及以上', 'style' => 'info'],
        '3' => ['text' => '本科及以上', 'style' => 'info'],
        '4' => ['text' => '研究生及以上', 'style' => 'info'],
        '5' => ['text' => '硕士', 'style' => 'info'],
    ];

    /**
     * 恐高要求定义
     */
    public $is_afraid_heights = [
        '0' => ['text' => '不限', 'style' => 'info'],
        '1' => ['text' => '不能恐高', 'style' => 'danger'],
        '2' => ['text' => '可以恐高', 'style' => 'success'],
    ];

    /**
     * 婚姻状况要求定义
     */
    public $marital_status = [
        '0' => ['text' => '不限', 'style' => 'info'],
        '1' => ['text' => '未婚', 'style' => 'primary'],
        '2' => ['text' => '已婚', 'style' => 'success'],
    ];

    /**
     * 获取岗位要求
     * @param int $postId 岗位ID
     * @param int $noticeId 公告ID
     * @return array|false
     */
    public function getRequirements($postId, $noticeId)
    {
        $requirements = $this->where([
            'post_id' => $postId,
            'notice_id' => $noticeId
        ])->find();
        
        if ($requirements) {
            $requirements['gender_info'] = $this->gender[$requirements['gender']];
            $requirements['education_level_info'] = $this->education_level[$requirements['education_level']];
            $requirements['is_afraid_heights_info'] = $this->is_afraid_heights[$requirements['is_afraid_heights'] ?: 0];
            $requirements['marital_status_info'] = $this->marital_status[$requirements['marital_status'] ?: 0];
        }
        
        return $requirements;
    }

    /**
     * 保存岗位要求
     * @param array $data 要求数据
     * @return int|bool
     */
    public function saveRequirements($data)
    {
        // 检查是否已存在
        $existing = $this->where([
            'post_id' => $data['post_id'],
            'notice_id' => $data['notice_id']
        ])->find();
        
        if ($existing) {
            // 更新
            $data['update_time'] = time();
            return $this->where(['id' => $existing['id']])->save($data);
        } else {
            // 新增
            $data['create_time'] = time();
            $data['update_time'] = time();
            return $this->add($data);
        }
    }

    /**
     * 批量保存岗位要求
     * @param array $postIds 岗位ID数组
     * @param int $noticeId 公告ID
     * @param array $requirements 要求数据
     * @return bool
     */
    public function batchSaveRequirements($postIds, $noticeId, $requirements)
    {
        foreach ($postIds as $postId) {
            $data = $requirements;
            $data['post_id'] = $postId;
            $data['notice_id'] = $noticeId;
            
            if (!$this->saveRequirements($data)) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * 获取招聘公告的所有岗位要求
     * @param int $noticeId 公告ID
     * @return array
     */
    public function getNoticeRequirements($noticeId)
    {
        $requirements = $this->alias('pr')
            ->join('LEFT JOIN __PROJECT_POST__ pp ON pr.post_id = pp.id')
            ->join('LEFT JOIN __PROJECT__ p ON pp.project_id = p.id')
            ->where(['pr.notice_id' => $noticeId])
            ->field('pr.*, pp.job_name, p.name as project_name')
            ->select();
            
        foreach ($requirements as &$item) {
            $item['gender_info'] = $this->gender[$item['gender']];
            $item['education_level_info'] = $this->education_level[$item['education_level']];
            $item['is_afraid_heights_info'] = $this->is_afraid_heights[$item['is_afraid_heights'] ?: 0];
            $item['marital_status_info'] = $this->marital_status[$item['marital_status'] ?: 0];
            
            // 格式化年龄范围
            $item['age_range'] = '';
            if ($item['min_age'] || $item['max_age']) {
                $item['age_range'] = ($item['min_age'] ?: '不限') . '-' . ($item['max_age'] ?: '不限') . '岁';
            }
            
            // 格式化身高范围
            $item['height_range'] = '';
            if ($item['min_height'] || $item['max_height']) {
                $item['height_range'] = ($item['min_height'] ?: '不限') . '-' . ($item['max_height'] ?: '不限') . 'cm';
            }
            
            // 格式化工作年限
            $item['work_years_range'] = '';
            if ($item['min_work_years'] || $item['max_work_years']) {
                $item['work_years_range'] = ($item['min_work_years'] ?: '不限') . '-' . ($item['max_work_years'] ?: '不限') . '年';
            }
        }
        
        return $requirements;
    }

    /**
     * 删除岗位要求
     * @param int $postId 岗位ID
     * @param int $noticeId 公告ID
     * @return bool
     */
    public function deleteRequirements($postId, $noticeId)
    {
        return $this->where([
            'post_id' => $postId,
            'notice_id' => $noticeId
        ])->delete();
    }

    /**
     * 根据要求筛选简历
     * @param array $requirements 要求条件
     * @return array 符合条件的简历ID数组
     */
    public function filterResumes($requirements)
    {
        $userJobModel = D('UserJob');
        $where = [];
        
        // 年龄筛选
        if ($requirements['min_age'] || $requirements['max_age']) {
            $currentYear = date('Y');
            if ($requirements['max_age']) {
                $minBirthYear = $currentYear - $requirements['max_age'];
                $where['birthdate'] = ['egt', $minBirthYear . '-01-01'];
            }
            if ($requirements['min_age']) {
                $maxBirthYear = $currentYear - $requirements['min_age'];
                if (isset($where['birthdate'])) {
                    $where['birthdate'] = [
                        ['egt', $minBirthYear . '-01-01'],
                        ['elt', $maxBirthYear . '-12-31']
                    ];
                } else {
                    $where['birthdate'] = ['elt', $maxBirthYear . '-12-31'];
                }
            }
        }
        
        // 性别筛选
        if ($requirements['gender'] > 0) {
            $where['gender'] = $requirements['gender'] == 1 ? '男' : '女';
        }
        
        // 身高筛选
        if ($requirements['min_height']) {
            $where['height'] = ['egt', $requirements['min_height']];
        }
        if ($requirements['max_height']) {
            if (isset($where['height'])) {
                $where['height'] = [
                    ['egt', $requirements['min_height']],
                    ['elt', $requirements['max_height']]
                ];
            } else {
                $where['height'] = ['elt', $requirements['max_height']];
            }
        }
        
        // 婚姻状况筛选
        if ($requirements['marital_status'] > 0) {
            $where['marital_status'] = $requirements['marital_status'] == 1 ? '未婚' : '已婚';
        }
        
        // 恐高筛选
        if ($requirements['is_afraid_heights'] > 0) {
            $where['is_afraid_heights'] = $requirements['is_afraid_heights'] == 1 ? 0 : 1;
        }
        
        $resumes = $userJobModel->where($where)->field('id')->select();
        return array_column($resumes, 'id');
    }
}
