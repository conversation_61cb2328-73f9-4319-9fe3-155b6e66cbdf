<?php
namespace Common\Model;

use Think\Model;

/**
 * 招聘公告模型
 */
class RecruitmentNoticeModel extends Model
{
    protected $_validate = [
        ['title', 'require', '公告标题不能为空'],
        ['title', '1,255', '公告标题长度不能超过255个字符', 0, 'length'],
        ['company_name', '1,255', '招聘单位长度不能超过255个字符', 0, 'length'],
        ['status', 'in:0,1', '状态值不正确', 0, 'regex'],
    ];

    protected $_auto = [
        ['create_time', 'time', self::MODEL_INSERT, 'function'],
        ['update_time', 'time', self::MODEL_BOTH, 'function'],
    ];

    /**
     * 状态列表
     */
    public $statusList = [
        0 => '停用',
        1 => '启用'
    ];

    /**
     * 获取状态文本
     */
    public function getStatusText($status)
    {
        return isset($this->statusList[$status]) ? $this->statusList[$status] : '未知';
    }

    /**
     * 获取公告详情（包含关联岗位）
     */
    public function getNoticeWithPosts($id)
    {
        $notice = $this->find($id);
        if (!$notice) {
            return false;
        }

        // 获取关联的岗位
        $posts = D("ProjectPost")->alias('pp')
            ->join('LEFT JOIN __RECRUITMENT_NOTICE_POST__ rnp ON pp.id = rnp.post_id')
            ->join('LEFT JOIN __PROJECT__ p ON pp.project_id = p.id')
            ->where(['rnp.notice_id' => $id])
            ->field('pp.*, p.name as project_name')
            ->select();

        $notice['posts'] = $posts ?: [];
        return $notice;
    }

    /**
     * 获取公告统计信息
     */
    public function getNoticeStats($id)
    {
        $stats = [];
        
        // 关联岗位数
        $stats['post_count'] = D("RecruitmentNoticePost")->where(['notice_id' => $id])->count();
        
        // 匹配简历总数
        $stats['total_matches'] = D("ResumePostMatch")->where(['notice_id' => $id])->count();
        
        // 符合条件的简历数
        $stats['qualified_matches'] = D("ResumePostMatch")->where(['notice_id' => $id, 'is_qualified' => 1])->count();
        
        // 平均匹配分数
        $avgScore = D("ResumePostMatch")->where(['notice_id' => $id, 'is_qualified' => 1])->avg('match_score');
        $stats['avg_score'] = $avgScore ? round($avgScore, 2) : 0;
        
        return $stats;
    }
}
