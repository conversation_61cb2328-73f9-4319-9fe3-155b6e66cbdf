<?php
namespace Common\Model;

use Common\Model\CommonModel;

/**
 * 招聘公告模型
 */
class RecruitmentNoticeModel extends CommonModel
{
    protected $_auto = [
        ['create_time', 'time', self::MODEL_INSERT, 'function'],
        ['update_time', 'time', self::MODEL_UPDATE, 'function'],
    ];

    protected $_validate = [
        ['title', 'require', '公告标题不能为空！', self::EXISTS_VALIDATE, 'regex', self::MODEL_BOTH],
        ['company_name', 'require', '招聘单位不能为空！', self::EXISTS_VALIDATE, 'regex', self::MODEL_BOTH],
    ];

    /**
     * 状态定义
     */
    public $status = [
        '0' => ['text' => '停用', 'style' => 'danger'],
        '1' => ['text' => '启用', 'style' => 'success'],
    ];

    /**
     * 获取招聘公告列表
     * @param array $where 查询条件
     * @param int $page 页码
     * @param int $limit 每页数量
     * @return array
     */
    public function getNoticeList($where = [], $page = 1, $limit = 20)
    {
        $page = max(1, intval($page)); // 确保页码至少为1
        $limit = max(1, intval($limit)); // 确保每页数量至少为1
        $offset = ($page - 1) * $limit;

        $list = $this->where($where)
            ->order('id DESC')
            ->limit($offset, $limit)
            ->select();
            
        foreach ($list as &$item) {
            $item['status_info'] = $this->status[$item['status']];
            $item['create_time_format'] = date('Y-m-d H:i:s', $item['create_time']);
            $item['update_time_format'] = $item['update_time'] ? date('Y-m-d H:i:s', $item['update_time']) : '';
            
            // 获取关联的岗位数量
            $item['post_count'] = D('RecruitmentNoticePost')->where(['notice_id' => $item['id']])->count();
        }
        
        return $list;
    }

    /**
     * 获取招聘公告详情
     * @param int $id 公告ID
     * @return array|false
     */
    public function getNoticeDetail($id)
    {
        $notice = $this->where(['id' => $id])->find();
        if (!$notice) {
            return false;
        }
        
        $notice['status_info'] = $this->status[$notice['status']];
        $notice['create_time_format'] = date('Y-m-d H:i:s', $notice['create_time']);
        $notice['update_time_format'] = $notice['update_time'] ? date('Y-m-d H:i:s', $notice['update_time']) : '';
        
        // 获取关联的岗位信息
        $notice['posts'] = $this->getNoticePosts($id);
        
        return $notice;
    }

    /**
     * 获取招聘公告关联的岗位
     * @param int $noticeId 公告ID
     * @return array
     */
    public function getNoticePosts($noticeId)
    {
        $posts = D('RecruitmentNoticePost')->alias('rnp')
            ->join('LEFT JOIN __PROJECT_POST__ pp ON rnp.post_id = pp.id')
            ->join('LEFT JOIN __PROJECT__ p ON pp.project_id = p.id')
            ->where(['rnp.notice_id' => $noticeId])
            ->field('rnp.*, pp.job_name, pp.service_price, pp.qualification, pp.sex, p.name as project_name')
            ->select();
            
        return $posts ?: [];
    }

    /**
     * 添加或更新招聘公告
     * @param array $data 数据
     * @return int|false
     */
    public function saveNotice($data)
    {
        if (isset($data['id']) && $data['id'] > 0) {
            // 更新
            $data['update_time'] = time();
            return $this->where(['id' => $data['id']])->save($data);
        } else {
            // 新增
            $data['create_time'] = time();
            $data['update_time'] = time();
            return $this->add($data);
        }
    }

    /**
     * 删除招聘公告
     * @param int $id 公告ID
     * @return bool
     */
    public function deleteNotice($id)
    {
        // 检查是否有关联的岗位
        $postCount = D('RecruitmentNoticePost')->where(['notice_id' => $id])->count();
        if ($postCount > 0) {
            $this->error = '该公告下还有关联的岗位，无法删除';
            return false;
        }
        
        // 删除匹配记录
        D('ResumePostMatch')->where(['notice_id' => $id])->delete();
        
        // 删除岗位要求
        D('PostRequirements')->where(['notice_id' => $id])->delete();
        
        // 删除公告
        return $this->where(['id' => $id])->delete();
    }

    /**
     * 更改状态
     * @param int $id 公告ID
     * @param int $status 状态
     * @return bool
     */
    public function changeStatus($id, $status)
    {
        if (!array_key_exists($status, $this->status)) {
            $this->error = '状态参数错误';
            return false;
        }
        
        return $this->where(['id' => $id])->save([
            'status' => $status,
            'update_time' => time()
        ]);
    }
}
