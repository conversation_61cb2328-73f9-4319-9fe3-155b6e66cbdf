<?php
/**
 * 招聘系统测试页面
 * 用于验证系统基本功能是否正常
 */

// 模拟ThinkPHP环境
define('THINK_PATH', './ThinkPHP/');
define('APP_PATH', './Application/');

echo "<h1>招聘系统测试页面</h1>";

// 测试1：检查文件是否存在
echo "<h2>1. 文件检查</h2>";
$files = [
    'Application/Prime/Controller/RecruitmentSystemController.class.php',
    'Application/Common/Model/RecruitmentNoticeModel.class.php',
    'Application/Common/Model/PostRequirementsModel.class.php',
    'Application/Common/Model/ResumePostMatchModel.class.php',
    'Application/Common/Model/RecruitmentNoticePostModel.class.php',
    'Application/Common/Service/ResumeMatchService.class.php',
];

foreach ($files as $file) {
    if (file_exists($file)) {
        echo "<span style='color:green'>✓</span> {$file}<br>";
    } else {
        echo "<span style='color:red'>✗</span> {$file}<br>";
    }
}

// 测试2：检查视图文件
echo "<h2>2. 视图文件检查</h2>";
$viewFiles = [
    'Application/Prime/View/RecruitmentSystem/index.html',
    'Application/Prime/View/RecruitmentSystem/add.html',
    'Application/Prime/View/RecruitmentSystem/edit.html',
    'Application/Prime/View/RecruitmentSystem/postConfig.html',
    'Application/Prime/View/RecruitmentSystem/requirements.html',
    'Application/Prime/View/RecruitmentSystem/matchResults.html',
    'Application/Prime/View/RecruitmentSystem/matchDetail.html',
];

foreach ($viewFiles as $file) {
    if (file_exists($file)) {
        echo "<span style='color:green'>✓</span> {$file}<br>";
    } else {
        echo "<span style='color:red'>✗</span> {$file}<br>";
    }
}

// 测试3：检查SQL文件
echo "<h2>3. SQL文件检查</h2>";
$sqlFiles = [
    'database_tables.sql',
    'menu_config.sql',
    'test_data.sql',
];

foreach ($sqlFiles as $file) {
    if (file_exists($file)) {
        echo "<span style='color:green'>✓</span> {$file}<br>";
    } else {
        echo "<span style='color:red'>✗</span> {$file}<br>";
    }
}

// 测试4：语法检查
echo "<h2>4. PHP语法检查</h2>";
$phpFiles = [
    'Application/Prime/Controller/RecruitmentSystemController.class.php',
    'Application/Common/Model/RecruitmentNoticeModel.class.php',
    'Application/Common/Model/PostRequirementsModel.class.php',
    'Application/Common/Model/ResumePostMatchModel.class.php',
    'Application/Common/Model/RecruitmentNoticePostModel.class.php',
    'Application/Common/Service/ResumeMatchService.class.php',
];

foreach ($phpFiles as $file) {
    if (file_exists($file)) {
        $output = [];
        $return_var = 0;
        exec("php -l {$file}", $output, $return_var);
        if ($return_var === 0) {
            echo "<span style='color:green'>✓</span> {$file} - 语法正确<br>";
        } else {
            echo "<span style='color:red'>✗</span> {$file} - 语法错误: " . implode(' ', $output) . "<br>";
        }
    }
}

echo "<h2>5. 系统状态</h2>";
echo "<p>如果所有文件都显示绿色的✓，说明系统文件部署正确。</p>";
echo "<p>接下来请：</p>";
echo "<ol>";
echo "<li>执行 database_tables.sql 创建数据库表</li>";
echo "<li>执行 menu_config.sql 配置菜单（根据实际菜单表结构调整）</li>";
echo "<li>在Prime后台访问招聘系统功能</li>";
echo "</ol>";

echo "<p><strong>注意：</strong>此测试页面仅用于验证文件部署，实际功能需要在ThinkPHP环境中运行。</p>";
?>
