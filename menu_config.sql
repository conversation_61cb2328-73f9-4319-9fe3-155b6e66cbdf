-- 招聘系统菜单配置SQL
-- 请根据实际的菜单表结构调整字段名和表名

-- 假设菜单表名为 z_menu，请根据实际情况修改
-- 插入主菜单：招聘系统
INSERT INTO `z_menu` (`name`, `title`, `url`, `icon`, `parent_id`, `sort`, `status`, `create_time`) 
VALUES ('招聘系统', '招聘系统', '', 'bullhorn', 0, 100, 1, UNIX_TIMESTAMP());

-- 获取刚插入的主菜单ID（假设为 @parent_id）
SET @parent_id = LAST_INSERT_ID();

-- 插入子菜单
INSERT INTO `z_menu` (`name`, `title`, `url`, `icon`, `parent_id`, `sort`, `status`, `create_time`) VALUES
('招聘公告管理', '招聘公告管理', 'RecruitmentSystem/index', 'list', @parent_id, 1, 1, UNIX_TIMESTAMP()),
('添加招聘公告', '添加招聘公告', 'RecruitmentSystem/add', 'plus', @parent_id, 2, 1, UNIX_TIMESTAMP()),
('岗位配置', '岗位配置', 'RecruitmentSystem/postConfig', 'cogs', @parent_id, 3, 1, UNIX_TIMESTAMP()),
('岗位要求配置', '岗位要求配置', 'RecruitmentSystem/requirements', 'cog', @parent_id, 4, 1, UNIX_TIMESTAMP()),
('匹配结果', '匹配结果', 'RecruitmentSystem/matchResults', 'eye', @parent_id, 5, 1, UNIX_TIMESTAMP()),
('执行筛选', '执行筛选', 'RecruitmentSystem/executeMatch', 'filter', @parent_id, 6, 1, UNIX_TIMESTAMP());

-- 注意：
-- 1. 请根据实际的菜单表结构调整字段名
-- 2. 请根据实际的权限系统调整相关配置
-- 3. 如果使用RBAC权限系统，可能需要额外配置权限节点
