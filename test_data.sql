-- 招聘系统测试数据
-- 用于测试系统功能，请在测试环境中使用

-- 插入测试招聘公告
INSERT INTO `z_recruitment_notice` (`title`, `description`, `company_name`, `status`, `create_time`, `update_time`) VALUES
('2024年春季校园招聘', '面向应届毕业生的校园招聘活动，提供多个技术岗位和管理岗位', '某科技有限公司', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('高级工程师招聘', '招聘有经验的高级工程师，要求本科以上学历', '某建筑集团', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('客服专员招聘', '招聘客服专员，要求沟通能力强，形象气质佳', '某服务公司', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 假设已有项目和岗位数据，这里插入一些关联关系
-- 注意：post_id 需要根据实际的岗位ID调整
INSERT INTO `z_recruitment_notice_post` (`notice_id`, `post_id`, `create_time`) VALUES
(1, 1, UNIX_TIMESTAMP()),
(1, 2, UNIX_TIMESTAMP()),
(2, 3, UNIX_TIMESTAMP()),
(3, 4, UNIX_TIMESTAMP());

-- 插入测试岗位要求
INSERT INTO `z_post_requirements` (`post_id`, `notice_id`, `min_age`, `max_age`, `gender`, `min_height`, `max_height`, `education_level`, `major_keywords`, `min_work_years`, `max_work_years`, `health_status`, `is_afraid_heights`, `political_status`, `marital_status`, `create_time`, `update_time`) VALUES
-- 软件工程师岗位要求
(1, 1, 22, 35, 0, 160, 190, 3, '计算机,软件工程,信息技术', 0, 5, '健康', 0, '', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
-- 产品经理岗位要求  
(2, 1, 25, 40, 0, 165, 185, 3, '市场营销,工商管理,产品设计', 2, 8, '健康', 0, '', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
-- 建筑工程师岗位要求
(3, 2, 25, 45, 1, 170, 185, 3, '土木工程,建筑学,工程管理', 3, 10, '健康', 1, '', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
-- 客服专员岗位要求
(4, 3, 20, 30, 2, 160, 175, 2, '服务管理,市场营销,中文', 0, 3, '健康', 0, '', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 注意事项：
-- 1. post_id 需要根据实际的岗位表数据调整
-- 2. 这些数据仅用于测试，生产环境请谨慎使用
-- 3. 执行前请确保相关的项目和岗位数据已存在

-- 查询语句，用于验证数据插入是否成功
-- SELECT * FROM z_recruitment_notice;
-- SELECT * FROM z_recruitment_notice_post;
-- SELECT * FROM z_post_requirements;
