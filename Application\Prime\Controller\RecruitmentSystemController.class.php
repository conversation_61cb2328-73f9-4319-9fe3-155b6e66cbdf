<?php
namespace Prime\Controller;

use Common\Controller\PrimeController;

/**
 * 招聘公告智能筛选系统
 * Class RecruitmentSystemController
 * @package Prime\Controller
 */
class RecruitmentSystemController extends PrimeController
{
    public function _initialize()
    {
        parent::_initialize();
    }

    /**
     * 招聘公告列表
     */
    public function index()
    {
        $c_kw = [
            'title' => '公告标题',
            'company_name' => '招聘单位',
            'id' => 'ID',
        ];
        $where = [];
        $s_kw = I("get.kw");
        $s_val = I("get.val");
        $s_status = I("get.status");

        if ($s_status !== '') $where['status'] = $s_status;
        if ($s_kw && $s_val != '' && in_array($s_kw, array_keys($c_kw))) {
            if (in_array($s_kw, ['title', 'company_name'])) {
                $where[$s_kw] = ['like', "%$s_val%"];
            } else {
                $where[$s_kw] = $s_val;
            }
        }

        $obj = D("RecruitmentNotice");
        $count = $obj->where($where)->count();
        $page = new \Think\Page($count, 20);
        $list = $obj->where($where)->order('id DESC')->limit($page->firstRow . ',' . $page->listRows)->select();

        // 获取关联的岗位数量
        foreach ($list as &$item) {
            $item['post_count'] = D("RecruitmentNoticePost")->where(['notice_id' => $item['id']])->count();
        }

        $this->assign('list', $list);
        $this->assign('page', $page->show());
        $this->assign('c_kw', $c_kw);
        $this->assign('s_kw', $s_kw);
        $this->assign('s_val', $s_val);
        $this->assign('s_status', $s_status);
        $this->display();
    }

    /**
     * 添加招聘公告
     */
    public function add()
    {
        $obj = D("RecruitmentNotice");
        if (IS_POST) {
            if ($data = $obj->create()) {
                $data['create_time'] = time();
                $data['update_time'] = time();
                if ($obj->add($data)) {
                    $this->success('添加成功', U('index'));
                } else {
                    $this->error('添加失败');
                }
            } else {
                $this->error($obj->getError());
            }
        } else {
            $this->display();
        }
    }

    /**
     * 编辑招聘公告
     */
    public function edit()
    {
        $id = I('get.id', 0, 'intval');
        $obj = D("RecruitmentNotice");
        
        if (IS_POST) {
            if ($data = $obj->create()) {
                $data['update_time'] = time();
                if ($obj->save($data)) {
                    $this->success('编辑成功', U('index'));
                } else {
                    $this->error('编辑失败');
                }
            } else {
                $this->error($obj->getError());
            }
        } else {
            $info = $obj->find($id);
            if (!$info) {
                $this->error('记录不存在');
            }
            $this->assign('info', $info);
            $this->display();
        }
    }

    /**
     * 删除招聘公告
     */
    public function delete()
    {
        $id = I('get.id', 0, 'intval');
        if (!$id) {
            $this->error('参数错误');
        }

        $obj = D("RecruitmentNotice");
        if ($obj->delete($id)) {
            // 同时删除关联的岗位和要求
            D("RecruitmentNoticePost")->where(['notice_id' => $id])->delete();
            D("PostRequirements")->where(['notice_id' => $id])->delete();
            D("ResumePostMatch")->where(['notice_id' => $id])->delete();
            
            $this->success('删除成功');
        } else {
            $this->error('删除失败');
        }
    }

    /**
     * 状态变更
     */
    public function changeStatus()
    {
        $id = I('get.id', 0, 'intval');
        $status = I('get.status', 0, 'intval');
        
        if (!$id) {
            $this->error('参数错误');
        }

        $obj = D("RecruitmentNotice");
        $data = ['id' => $id, 'status' => $status, 'update_time' => time()];
        
        if ($obj->save($data)) {
            $this->success('状态更新成功');
        } else {
            $this->error('状态更新失败');
        }
    }

    /**
     * 岗位配置
     */
    public function postConfig()
    {
        $notice_id = I('get.notice_id', 0, 'intval');
        if (!$notice_id) {
            $this->error('参数错误');
        }

        $notice = D("RecruitmentNotice")->find($notice_id);
        if (!$notice) {
            $this->error('招聘公告不存在');
        }

        if (IS_POST) {
            $post_ids = I('post.post_ids', []);
            if (empty($post_ids)) {
                $this->error('请选择岗位');
            }

            // 删除原有关联
            D("RecruitmentNoticePost")->where(['notice_id' => $notice_id])->delete();
            
            // 添加新关联
            $data = [];
            foreach ($post_ids as $post_id) {
                $data[] = [
                    'notice_id' => $notice_id,
                    'post_id' => $post_id,
                    'create_time' => time()
                ];
            }
            
            if (D("RecruitmentNoticePost")->addAll($data)) {
                $this->success('配置成功', U('index'));
            } else {
                $this->error('配置失败');
            }
        } else {
            // 获取所有可用岗位
            $postList = D("ProjectPost")->where(['status' => 1])->select();
            
            // 获取已关联的岗位
            $selectedPosts = D("RecruitmentNoticePost")->where(['notice_id' => $notice_id])->getField('post_id', true);
            
            $this->assign('notice', $notice);
            $this->assign('postList', $postList);
            $this->assign('selectedPosts', $selectedPosts);
            $this->display();
        }
    }

    /**
     * 岗位要求配置
     */
    public function requirements()
    {
        $notice_id = I('get.notice_id', 0, 'intval');
        $post_id = I('get.post_id', 0, 'intval');
        
        if (!$notice_id || !$post_id) {
            $this->error('参数错误');
        }

        $notice = D("RecruitmentNotice")->find($notice_id);
        $post = D("ProjectPost")->find($post_id);
        
        if (!$notice || !$post) {
            $this->error('记录不存在');
        }

        $obj = D("PostRequirements");
        
        if (IS_POST) {
            $data = I('post.');
            $data['notice_id'] = $notice_id;
            $data['post_id'] = $post_id;
            
            // 检查是否已存在
            $existing = $obj->where(['notice_id' => $notice_id, 'post_id' => $post_id])->find();
            
            if ($existing) {
                $data['id'] = $existing['id'];
                $data['update_time'] = time();
                if ($obj->save($data)) {
                    $this->success('更新成功', U('index'));
                } else {
                    $this->error('更新失败');
                }
            } else {
                $data['create_time'] = time();
                $data['update_time'] = time();
                if ($obj->add($data)) {
                    $this->success('添加成功', U('index'));
                } else {
                    $this->error('添加失败');
                }
            }
        } else {
            $requirements = $obj->where(['notice_id' => $notice_id, 'post_id' => $post_id])->find();
            
            $this->assign('notice', $notice);
            $this->assign('post', $post);
            $this->assign('requirements', $requirements);
            $this->display();
        }
    }

    /**
     * 执行简历筛选
     */
    public function executeMatch()
    {
        $notice_id = I('get.notice_id', 0, 'intval');
        if (!$notice_id) {
            $this->error('参数错误');
        }

        $notice = D("RecruitmentNotice")->find($notice_id);
        if (!$notice) {
            $this->error('招聘公告不存在');
        }

        // 获取该公告的所有岗位要求
        $requirements = D("PostRequirements")->where(['notice_id' => $notice_id])->select();
        if (empty($requirements)) {
            $this->error('请先配置岗位要求');
        }

        // 获取所有简历
        $resumes = D("UserJob")->where(['type' => 2])->select();
        
        $matchService = new \Common\Service\ResumeMatchService();
        $totalMatched = 0;
        
        foreach ($requirements as $requirement) {
            foreach ($resumes as $resume) {
                $matchResult = $matchService->matchResume($resume, $requirement);
                
                // 保存匹配结果
                $matchData = [
                    'user_job_id' => $resume['id'],
                    'post_id' => $requirement['post_id'],
                    'notice_id' => $notice_id,
                    'match_score' => $matchResult['score'],
                    'match_details' => json_encode($matchResult['details']),
                    'is_qualified' => $matchResult['qualified'] ? 1 : 0,
                    'create_time' => time(),
                    'update_time' => time()
                ];
                
                // 使用REPLACE INTO避免重复
                $existing = D("ResumePostMatch")->where([
                    'user_job_id' => $resume['id'],
                    'post_id' => $requirement['post_id'],
                    'notice_id' => $notice_id
                ])->find();
                
                if ($existing) {
                    $matchData['id'] = $existing['id'];
                    D("ResumePostMatch")->save($matchData);
                } else {
                    D("ResumePostMatch")->add($matchData);
                }
                
                if ($matchResult['qualified']) {
                    $totalMatched++;
                }
            }
        }
        
        $this->success("筛选完成，共匹配到 {$totalMatched} 条符合条件的简历", U('matchResults', ['notice_id' => $notice_id]));
    }

    /**
     * 匹配结果展示
     */
    public function matchResults()
    {
        $notice_id = I('get.notice_id', 0, 'intval');
        $post_id = I('get.post_id', 0, 'intval');
        $qualified = I('get.qualified', '');

        if (!$notice_id) {
            $this->error('参数错误');
        }

        $notice = D("RecruitmentNotice")->find($notice_id);
        if (!$notice) {
            $this->error('招聘公告不存在');
        }

        $where = ['m.notice_id' => $notice_id];
        if ($post_id) {
            $where['m.post_id'] = $post_id;
        }
        if ($qualified !== '') {
            $where['m.is_qualified'] = $qualified;
        }

        // 获取匹配结果
        $matchModel = D("ResumePostMatch");
        $count = $matchModel->alias('m')
            ->join('LEFT JOIN __USER_JOB__ uj ON m.user_job_id = uj.id')
            ->join('LEFT JOIN __PROJECT_POST__ pp ON m.post_id = pp.id')
            ->where($where)
            ->count();

        $page = new \Think\Page($count, 20);
        $list = $matchModel->alias('m')
            ->join('LEFT JOIN __USER_JOB__ uj ON m.user_job_id = uj.id')
            ->join('LEFT JOIN __PROJECT_POST__ pp ON m.post_id = pp.id')
            ->join('LEFT JOIN __PROJECT__ p ON pp.project_id = p.id')
            ->where($where)
            ->field('m.*, uj.name, uj.gender, uj.phone, uj.education_level, pp.job_name, p.name as project_name')
            ->order('m.match_score DESC, m.id DESC')
            ->limit($page->firstRow . ',' . $page->listRows)
            ->select();

        // 解析匹配详情
        foreach ($list as &$item) {
            $item['match_details'] = json_decode($item['match_details'], true);
        }

        // 获取该公告的所有岗位
        $postList = D("ProjectPost")->alias('pp')
            ->join('LEFT JOIN __RECRUITMENT_NOTICE_POST__ rnp ON pp.id = rnp.post_id')
            ->join('LEFT JOIN __PROJECT__ p ON pp.project_id = p.id')
            ->where(['rnp.notice_id' => $notice_id])
            ->field('pp.id, pp.job_name, p.name as project_name')
            ->select();

        $this->assign('notice', $notice);
        $this->assign('list', $list);
        $this->assign('page', $page->show());
        $this->assign('postList', $postList);
        $this->assign('post_id', $post_id);
        $this->assign('qualified', $qualified);
        $this->display();
    }

    /**
     * 查看匹配详情
     */
    public function matchDetail()
    {
        $id = I('get.id', 0, 'intval');
        if (!$id) {
            $this->error('参数错误');
        }

        $match = D("ResumePostMatch")->alias('m')
            ->join('LEFT JOIN __USER_JOB__ uj ON m.user_job_id = uj.id')
            ->join('LEFT JOIN __PROJECT_POST__ pp ON m.post_id = pp.id')
            ->join('LEFT JOIN __PROJECT__ p ON pp.project_id = p.id')
            ->join('LEFT JOIN __RECRUITMENT_NOTICE__ rn ON m.notice_id = rn.id')
            ->where(['m.id' => $id])
            ->field('m.*, uj.*, pp.job_name, p.name as project_name, rn.title as notice_title')
            ->find();

        if (!$match) {
            $this->error('记录不存在');
        }

        // 获取岗位要求
        $requirements = D("PostRequirements")->where([
            'notice_id' => $match['notice_id'],
            'post_id' => $match['post_id']
        ])->find();

        $match['match_details'] = json_decode($match['match_details'], true);

        $this->assign('match', $match);
        $this->assign('requirements', $requirements);
        $this->display();
    }

    /**
     * 导出匹配结果
     */
    public function exportResults()
    {
        $notice_id = I('get.notice_id', 0, 'intval');
        if (!$notice_id) {
            $this->error('参数错误');
        }

        $notice = D("RecruitmentNotice")->find($notice_id);
        if (!$notice) {
            $this->error('招聘公告不存在');
        }

        // 获取所有匹配结果
        $list = D("ResumePostMatch")->alias('m')
            ->join('LEFT JOIN __USER_JOB__ uj ON m.user_job_id = uj.id')
            ->join('LEFT JOIN __PROJECT_POST__ pp ON m.post_id = pp.id')
            ->join('LEFT JOIN __PROJECT__ p ON pp.project_id = p.id')
            ->where(['m.notice_id' => $notice_id, 'm.is_qualified' => 1])
            ->field('uj.name, uj.gender, uj.phone, uj.education_level, uj.major, pp.job_name, p.name as project_name, m.match_score')
            ->order('m.match_score DESC')
            ->select();

        // 生成CSV文件
        $filename = "招聘匹配结果_" . $notice['title'] . "_" . date('Y-m-d') . ".csv";

        header('Content-Type: application/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Pragma: public');

        $output = fopen('php://output', 'w');

        // 写入BOM头，解决中文乱码
        fwrite($output, "\xEF\xBB\xBF");

        // 写入表头
        fputcsv($output, ['姓名', '性别', '电话', '学历', '专业', '岗位', '项目', '匹配分数']);

        // 写入数据
        foreach ($list as $row) {
            fputcsv($output, [
                $row['name'],
                $row['gender'],
                $row['phone'],
                $row['education_level'],
                $row['major'],
                $row['job_name'],
                $row['project_name'],
                $row['match_score']
            ]);
        }

        fclose($output);
        exit;
    }
}
