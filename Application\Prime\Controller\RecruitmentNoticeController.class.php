<?php
namespace Prime\Controller;

use Common\Controller\PrimeController;

/**
 * 招聘公告管理控制器
 */
class RecruitmentNoticeController extends PrimeController
{
    public function _initialize()
    {
        parent::_initialize();
    }

    /**
     * 安全的分页处理
     * @param int $count 总记录数
     * @param int $listRows 每页记录数
     * @return object 分页对象
     */
    protected function safePage($count, $listRows = 20)
    {
        $count = max(0, intval($count));
        $listRows = max(1, intval($listRows));

        $page = $this->page($count, $listRows);

        // 确保当前页码有效
        if (!isset($page->nowPage) || $page->nowPage < 1) {
            $page->nowPage = 1;
        } else {
            $page->nowPage = max(1, intval($page->nowPage));
            $maxPage = $count > 0 ? ceil($count / $listRows) : 1;
            $page->nowPage = min($page->nowPage, $maxPage);
        }

        return $page;
    }

    /**
     * 招聘公告列表
     */
    public function index()
    {
        $where = [];
        
        // 搜索条件
        $title = I('get.title', '');
        $company_name = I('get.company_name', '');
        $status = I('get.status', '');
        
        if ($title) {
            $where['title'] = ['like', '%' . $title . '%'];
        }
        if ($company_name) {
            $where['company_name'] = ['like', '%' . $company_name . '%'];
        }
        if ($status !== '') {
            $where['status'] = $status;
        }
        
        $obj = D('RecruitmentNotice');
        $count = $obj->where($where)->count();
        $page = $this->safePage($count, 20);

        $list = $obj->getNoticeList($where, $page->nowPage, 20);
        
        $this->assign('list', $list);
        $this->assign('page', $page->show());
        $this->assign('search', [
            'title' => $title,
            'company_name' => $company_name,
            'status' => $status
        ]);
        $this->assign('status_options', $obj->status);
        
        $this->display();
    }

    /**
     * 新增/编辑招聘公告
     */
    public function edit()
    {
        $id = intval(I('get.id'));
        $obj = D('RecruitmentNotice');
        
        if ($id) {
            $row = $obj->getNoticeDetail($id);
            if (!$row) {
                $this->error('招聘公告不存在');
            }
            $this->assign('row', $row);
        }
        
        if (IS_POST) {
            $data = [
                'title' => I('post.title'),
                'description' => I('post.description'),
                'company_name' => I('post.company_name'),
                'status' => intval(I('post.status', 1))
            ];
            
            if ($id) {
                $data['id'] = $id;
            }
            
            if ($obj->create($data)) {
                $result = $obj->saveNotice($data);
                if ($result !== false) {
                    $this->success('操作成功', U('recruitmentnotice/index'));
                } else {
                    $this->error('操作失败');
                }
            } else {
                $this->error($obj->getError());
            }
        }
        
        $this->assign('status_options', $obj->status);
        $this->display();
    }

    /**
     * 查看招聘公告详情
     */
    public function detail()
    {
        $id = intval(I('get.id'));
        if (!$id) {
            $this->error('参数错误');
        }
        
        $obj = D('RecruitmentNotice');
        $notice = $obj->getNoticeDetail($id);
        if (!$notice) {
            $this->error('招聘公告不存在');
        }
        
        // 获取岗位要求配置
        $requirements = D('PostRequirements')->getNoticeRequirements($id);
        
        // 获取匹配统计
        $matchStats = D('ResumePostMatch')->getMatchStats($id);
        
        $this->assign('notice', $notice);
        $this->assign('requirements', $requirements);
        $this->assign('match_stats', $matchStats);
        
        $this->display();
    }

    /**
     * 状态变更
     */
    public function cgstat()
    {
        $id = intval(I('get.id'));
        $status = intval(I('get.status'));
        
        if (!$id) {
            $this->error('参数错误');
        }
        
        $obj = D('RecruitmentNotice');
        if ($obj->changeStatus($id, $status)) {
            $this->success('状态修改成功');
        } else {
            $this->error($obj->getError() ?: '状态修改失败');
        }
    }

    /**
     * 删除招聘公告
     */
    public function delete()
    {
        $id = intval(I('get.id'));
        if (!$id) {
            $this->error('参数错误');
        }
        
        $obj = D('RecruitmentNotice');
        if ($obj->deleteNotice($id)) {
            $this->success('删除成功');
        } else {
            $this->error($obj->getError() ?: '删除失败');
        }
    }

    /**
     * 岗位关联管理
     */
    public function posts()
    {
        $id = intval(I('get.id'));
        if (!$id) {
            $this->error('参数错误');
        }
        
        $obj = D('RecruitmentNotice');
        $notice = $obj->where(['id' => $id])->find();
        if (!$notice) {
            $this->error('招聘公告不存在');
        }
        
        if (IS_POST) {
            $post_ids = I('post.post_ids', []);
            
            // 删除原有关联
            D('RecruitmentNoticePost')->where(['notice_id' => $id])->delete();
            
            // 添加新关联
            if (!empty($post_ids)) {
                $data = [];
                foreach ($post_ids as $post_id) {
                    $data[] = [
                        'notice_id' => $id,
                        'post_id' => $post_id,
                        'create_time' => time()
                    ];
                }
                D('RecruitmentNoticePost')->addAll($data);
            }
            
            $this->success('岗位关联更新成功', U('recruitmentnotice/posts', ['id' => $id]));
        }
        
        // 获取所有可用岗位
        $allPosts = D('ProjectPost')->alias('pp')
            ->join('LEFT JOIN __PROJECT__ p ON pp.project_id = p.id')
            ->where(['pp.status' => 1])
            ->field('pp.id, pp.job_name, p.name as project_name')
            ->order('p.id DESC, pp.id DESC')
            ->select();
        
        // 获取已关联的岗位
        $linkedPosts = D('RecruitmentNoticePost')->where(['notice_id' => $id])->getField('post_id', true);
        
        $this->assign('notice', $notice);
        $this->assign('all_posts', $allPosts);
        $this->assign('linked_posts', $linkedPosts);
        
        $this->display();
    }

    /**
     * 批量操作
     */
    public function batch()
    {
        $action = I('post.action');
        $ids = I('post.ids', []);
        
        if (empty($ids)) {
            $this->error('请选择要操作的记录');
        }
        
        $obj = D('RecruitmentNotice');
        
        switch ($action) {
            case 'delete':
                $success = 0;
                foreach ($ids as $id) {
                    if ($obj->deleteNotice($id)) {
                        $success++;
                    }
                }
                $this->success("成功删除 {$success} 条记录");
                break;
                
            case 'enable':
                $result = $obj->where(['id' => ['in', $ids]])->save([
                    'status' => 1,
                    'update_time' => time()
                ]);
                if ($result !== false) {
                    $this->success('批量启用成功');
                } else {
                    $this->error('批量启用失败');
                }
                break;
                
            case 'disable':
                $result = $obj->where(['id' => ['in', $ids]])->save([
                    'status' => 0,
                    'update_time' => time()
                ]);
                if ($result !== false) {
                    $this->success('批量停用成功');
                } else {
                    $this->error('批量停用失败');
                }
                break;
                
            default:
                $this->error('无效的操作');
        }
    }
}
