<include file="block/hat" />
<div class="container-fluid">
    <div class="row">
        <include file="block/menu" />
        <div class="col-xs-12 col-sm-9 col-lg-10">
            <ul class="nav nav-tabs">
                <li class="active"><a>简历匹配结果</a></li>
            </ul>
            <style type='text/css'>.tab-pane {padding:20px 0 20px 0;}</style>
            
            <div class="main">
                <!-- 招聘公告信息 -->
                <div class="panel panel-info">
                    <div class="panel-heading">
                        <h3 class="panel-title">
                            <i class="fa fa-info-circle"></i> 招聘公告信息
                            <div class="pull-right">
                                <form method="post" action="{:U('resumematch/do_match')}" style="display: inline;">
                                    <input type="hidden" name="notice_id" value="{$notice.id}" />
                                    <button type="submit" class="btn btn-xs btn-primary" onclick="return confirm('确定要重新执行匹配吗？这将清除现有匹配结果。')">
                                        <i class="fa fa-refresh"></i> 重新匹配
                                    </button>
                                </form>
                                <a href="{:U('resumematch/export', array('notice_id'=>$notice['id']))}" class="btn btn-xs btn-success">
                                    <i class="fa fa-download"></i> 导出结果
                                </a>
                            </div>
                        </h3>
                    </div>
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-8">
                                <h4>{$notice.title}</h4>
                                <p class="text-muted">招聘单位：{$notice.company_name}</p>
                            </div>
                            <div class="col-md-4 text-right">
                                <a href="{:U('recruitmentnotice/detail', array('id'=>$notice['id']))}" class="btn btn-default">
                                    <i class="fa fa-arrow-left"></i> 返回详情
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 匹配统计 -->
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h3 class="panel-title">
                            <i class="fa fa-bar-chart"></i> 匹配统计
                        </h3>
                    </div>
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="stat-box text-center">
                                    <div class="stat-number text-primary">{$stats.total_matches}</div>
                                    <div class="stat-label">总匹配数</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stat-box text-center">
                                    <div class="stat-number text-success">{$stats.qualified_matches}</div>
                                    <div class="stat-label">符合要求</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stat-box text-center">
                                    <div class="stat-number text-warning">{$stats.unqualified_matches}</div>
                                    <div class="stat-label">不符合要求</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stat-box text-center">
                                    <div class="stat-number text-info">{$stats.avg_score}</div>
                                    <div class="stat-label">平均分数</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 搜索面板 -->
                <div class="panel panel-default">
                    <div class="panel-body">
                        <form method="get" class="form-inline" role="form">
                            <input type="hidden" name="notice_id" value="{$notice.id}" />
                            <div class="form-group">
                                <label>岗位筛选：</label>
                                <select class="form-control" name="post_id">
                                    <option value="">全部岗位</option>
                                    <php>foreach($posts as $post){</php>
                                    <option value="{$post.post_id}" <php>if($post['post_id'] == $filters['post_id']){</php>selected<php>}</php>>{$post.job_name}</option>
                                    <php>}</php>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>符合要求：</label>
                                <select class="form-control" name="is_qualified">
                                    <option value="">全部</option>
                                    <php>foreach($qualified_options as $key=>$value){</php>
                                    <option value="{$key}" <php>if($key == $filters['is_qualified']){</php>selected<php>}</php>>{$value.text}</option>
                                    <php>}</php>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>分数范围：</label>
                                <input class="form-control" type="number" name="min_score" value="{$filters.min_score}" placeholder="最低分" style="width: 80px;" min="0" max="100" />
                                <span>-</span>
                                <input class="form-control" type="number" name="max_score" value="{$filters.max_score}" placeholder="最高分" style="width: 80px;" min="0" max="100" />
                            </div>
                            <button type="submit" class="btn btn-primary"><i class="fa fa-search"></i> 搜索</button>
                            <a href="{:U('resumematch/index', array('notice_id'=>$notice['id']))}" class="btn btn-warning">重置</a>
                        </form>
                    </div>
                </div>

                <!-- 匹配结果列表 -->
                <div class="panel panel-default">
                    <div class="panel-body table-responsive">
                        <table class="table table-hover">
                            <thead class="navbar-inner">
                                <tr>
                                    <th width="80">ID</th>
                                    <th>姓名</th>
                                    <th>性别</th>
                                    <th>年龄</th>
                                    <th>电话</th>
                                    <th>学历</th>
                                    <th>专业</th>
                                    <th>身高</th>
                                    <th>岗位</th>
                                    <th width="80">匹配分数</th>
                                    <th width="80">是否符合</th>
                                    <th width="150">匹配时间</th>
                                    <th width="100">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <php>if(empty($list)){</php>
                                <tr>
                                    <td colspan="13" class="text-center text-muted">暂无匹配结果</td>
                                </tr>
                                <php>}else{</php>
                                <php>foreach($list as $item){</php>
                                <tr>
                                    <td>{$item.user_job_id}</td>
                                    <td>
                                        <strong>{$item.name}</strong>
                                    </td>
                                    <td>{$item.gender}</td>
                                    <td>{$item.age|default='-'}</td>
                                    <td>{$item.phone}</td>
                                    <td>{$item.education_level|default='-'}</td>
                                    <td>{$item.major|default='-'}</td>
                                    <td>{$item.height|default='-'}cm</td>
                                    <td>
                                        <small class="text-muted">{$item.project_name}</small><br>
                                        {$item.job_name}
                                    </td>
                                    <td>
                                        <span class="badge <php>if($item['match_score'] >= 80){</php>badge-success<php>}elseif($item['match_score'] >= 60){</php>badge-warning<php>}else{</php>badge-danger<php>}</php>">
                                            {$item.match_score}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="label label-{$item.is_qualified_info.style}">{$item.is_qualified_info.text}</span>
                                    </td>
                                    <td>{$item.create_time_format}</td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="{:U('resumematch/detail', array('id'=>$item['id']))}" class="btn btn-xs btn-info" title="查看详情">
                                                <i class="fa fa-eye"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                <php>}</php>
                                <php>}</php>
                            </tbody>
                        </table>
                        
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="pull-right">
                                    {$page}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 按岗位统计 -->
                <php>if(!empty($stats['post_stats'])){</php>
                <div class="panel panel-success">
                    <div class="panel-heading">
                        <h3 class="panel-title">
                            <i class="fa fa-pie-chart"></i> 按岗位统计
                        </h3>
                    </div>
                    <div class="panel-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>岗位名称</th>
                                        <th>总匹配数</th>
                                        <th>符合要求</th>
                                        <th>不符合要求</th>
                                        <th>平均分数</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <php>foreach($stats['post_stats'] as $postStat){</php>
                                    <tr>
                                        <td>{$postStat.job_name}</td>
                                        <td><span class="badge badge-primary">{$postStat.total_count}</span></td>
                                        <td><span class="badge badge-success">{$postStat.qualified_count}</span></td>
                                        <td><span class="badge badge-warning">{$postStat.unqualified_count}</span></td>
                                        <td><span class="badge badge-info">{$postStat.avg_score}</span></td>
                                        <td>
                                            <a href="{:U('resumematch/post_matches', array('notice_id'=>$notice['id'], 'post_id'=>$postStat['post_id']))}" class="btn btn-xs btn-primary">
                                                <i class="fa fa-list"></i> 查看详情
                                            </a>
                                        </td>
                                    </tr>
                                    <php>}</php>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <php>}</php>
            </div>
        </div>
    </div>
</div>

<style>
.stat-box {
    padding: 20px;
    border: 1px solid #e7e7e7;
    border-radius: 4px;
    margin-bottom: 20px;
}
.stat-number {
    font-size: 36px;
    font-weight: bold;
    margin-bottom: 5px;
}
.stat-label {
    font-size: 14px;
    color: #666;
}
</style>

<include file="block/footer" />
