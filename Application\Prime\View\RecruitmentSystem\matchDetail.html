<include file="block/hat" />
<div class="container-fluid">
    <div class="row">
        <include file="block/menu" />
        <div class="col-xs-12 col-sm-9 col-lg-10">
            <ul class="nav nav-tabs">
                <li class="active"><a>匹配详情</a></li>
            </ul>
            <style type='text/css'>.tab-pane {padding:20px 0 20px 0;}</style>
            <div class="main">
                <div class="row">
                    <!-- 基本信息 -->
                    <div class="col-md-6">
                        <div class="panel panel-primary">
                            <div class="panel-heading">
                                <h4 class="panel-title">
                                    <i class="fa fa-user"></i> 简历信息
                                </h4>
                            </div>
                            <div class="panel-body">
                                <table class="table table-bordered">
                                    <tr>
                                        <td width="30%"><strong>姓名</strong></td>
                                        <td>{$match.name}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>性别</strong></td>
                                        <td>{$match.gender}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>年龄</strong></td>
                                        <td>
                                            <?php
                                            if (!empty($match['id_number']) && strlen($match['id_number']) == 18) {
                                                $birthYear = substr($match['id_number'], 6, 4);
                                                $age = date('Y') - $birthYear;
                                                echo $age . '岁';
                                            } else {
                                                echo '未知';
                                            }
                                            ?>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>身高</strong></td>
                                        <td>{$match.height}cm</td>
                                    </tr>
                                    <tr>
                                        <td><strong>学历</strong></td>
                                        <td>{$match.education_level}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>专业</strong></td>
                                        <td>{$match.major}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>工作经验</strong></td>
                                        <td>{$match.work_experience_years}年</td>
                                    </tr>
                                    <tr>
                                        <td><strong>健康状况</strong></td>
                                        <td>{$match.health_status}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>恐高情况</strong></td>
                                        <td>{$match.is_afraid_heights}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>政治面貌</strong></td>
                                        <td>{$match.political_status}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>婚姻状况</strong></td>
                                        <td>{$match.marital_status}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>联系电话</strong></td>
                                        <td>{$match.phone}</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 岗位要求 -->
                    <div class="col-md-6">
                        <div class="panel panel-info">
                            <div class="panel-heading">
                                <h4 class="panel-title">
                                    <i class="fa fa-briefcase"></i> 岗位要求
                                </h4>
                            </div>
                            <div class="panel-body">
                                <h5><strong>{$match.project_name} - {$match.job_name}</strong></h5>
                                <hr>
                                <table class="table table-bordered">
                                    <tr>
                                        <td width="30%"><strong>年龄要求</strong></td>
                                        <td>
                                            <if condition="!empty($requirements['min_age']) || !empty($requirements['max_age'])">
                                                {$requirements.min_age|default="不限"} - {$requirements.max_age|default="不限"}岁
                                            <else />
                                                不限
                                            </if>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>性别要求</strong></td>
                                        <td>
                                            <switch name="requirements.gender">
                                                <case value="1">男</case>
                                                <case value="2">女</case>
                                                <default />不限
                                            </switch>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>身高要求</strong></td>
                                        <td>
                                            <if condition="!empty($requirements['min_height']) || !empty($requirements['max_height'])">
                                                {$requirements.min_height|default="不限"} - {$requirements.max_height|default="不限"}cm
                                            <else />
                                                不限
                                            </if>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>学历要求</strong></td>
                                        <td>
                                            <switch name="requirements.education_level">
                                                <case value="1">中专及以上</case>
                                                <case value="2">大专及以上</case>
                                                <case value="3">本科及以上</case>
                                                <case value="4">研究生及以上</case>
                                                <case value="5">硕士及以上</case>
                                                <default />不限
                                            </switch>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>专业要求</strong></td>
                                        <td>{$requirements.major_keywords|default="不限"}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>工作经验</strong></td>
                                        <td>
                                            <if condition="!empty($requirements['min_work_years']) || !empty($requirements['max_work_years'])">
                                                {$requirements.min_work_years|default="不限"} - {$requirements.max_work_years|default="不限"}年
                                            <else />
                                                不限
                                            </if>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>健康状况</strong></td>
                                        <td>{$requirements.health_status|default="不限"}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>恐高要求</strong></td>
                                        <td>
                                            <switch name="requirements.is_afraid_heights">
                                                <case value="1">不能恐高</case>
                                                <case value="2">可以恐高</case>
                                                <default />不限
                                            </switch>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>政治面貌</strong></td>
                                        <td>{$requirements.political_status|default="不限"}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>婚姻状况</strong></td>
                                        <td>
                                            <switch name="requirements.marital_status">
                                                <case value="1">未婚</case>
                                                <case value="2">已婚</case>
                                                <default />不限
                                            </switch>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 匹配结果 -->
                <div class="row">
                    <div class="col-md-12">
                        <div class="panel panel-success">
                            <div class="panel-heading">
                                <h4 class="panel-title">
                                    <i class="fa fa-check-circle"></i> 匹配结果
                                    <span class="pull-right">
                                        总分：<strong>{$match.match_score}分</strong>
                                        <if condition="$match.is_qualified eq 1">
                                            <span class="label label-success">符合条件</span>
                                        <else />
                                            <span class="label label-danger">不符合条件</span>
                                        </if>
                                    </span>
                                </h4>
                            </div>
                            <div class="panel-body">
                                <if condition="!empty($match['match_details'])">
                                    <div class="row">
                                        <volist name="match.match_details" id="detail" key="category">
                                            <div class="col-md-6">
                                                <div class="panel panel-default">
                                                    <div class="panel-heading">
                                                        <h6>
                                                            <switch name="category">
                                                                <case value="age">年龄匹配</case>
                                                                <case value="gender">性别匹配</case>
                                                                <case value="height">身高匹配</case>
                                                                <case value="education">学历匹配</case>
                                                                <case value="major">专业匹配</case>
                                                                <case value="work_years">工作经验匹配</case>
                                                                <case value="other">其他条件匹配</case>
                                                                <default />{$category}
                                                            </switch>
                                                        </h6>
                                                    </div>
                                                    <div class="panel-body">
                                                        <div class="progress" style="margin-bottom: 10px;">
                                                            <div class="progress-bar 
                                                                <if condition="$detail['qualified']">progress-bar-success
                                                                <else />progress-bar-danger
                                                                </if>" 
                                                                style="width: {$detail.score}%">
                                                            </div>
                                                        </div>
                                                        <p>
                                                            <strong>得分：{$detail.score}分</strong>
                                                            <if condition="$detail['qualified']">
                                                                <span class="label label-success">符合</span>
                                                            <else />
                                                                <span class="label label-danger">不符合</span>
                                                            </if>
                                                        </p>
                                                        <p class="text-muted">{$detail.message}</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </volist>
                                    </div>
                                <else />
                                    <div class="alert alert-warning">
                                        <i class="fa fa-exclamation-triangle"></i> 暂无详细匹配信息
                                    </div>
                                </if>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-12">
                        <a href="{:U('matchResults', ['notice_id' => $match['notice_id']])}" class="btn btn-default" style="padding: 12px 24px; font-size: 14px; font-weight: 600;">
                            <i class="fa fa-arrow-left"></i> 返回匹配结果
                        </a>
                        <a href="{:U('Userjob/joballinfo', ['id' => $match['user_job_id']])}" class="btn btn-primary" target="_blank" style="padding: 12px 24px; font-size: 14px; font-weight: 600; margin-left: 10px;">
                            <i class="fa fa-file-text"></i> 查看完整简历
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<include file="block/footer" />
