<include file="block/hat" />
<div class="container-fluid">
    <div class="row">
        <include file="block/menu" />
        <div class="col-xs-12 col-sm-9 col-lg-10">
            <ul class="nav nav-tabs">
                <li class="active"><a>招聘公告详情</a></li>
            </ul>
            <style type='text/css'>.tab-pane {padding:20px 0 20px 0;}</style>
            
            <div class="main">
                <!-- 基本信息 -->
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h3 class="panel-title">
                            <i class="fa fa-info-circle"></i> 基本信息
                            <div class="pull-right">
                                <span class="label label-{$notice.status_info.style}">{$notice.status_info.text}</span>
                            </div>
                        </h3>
                    </div>
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-bordered">
                                    <tr>
                                        <td width="120" class="text-right"><strong>公告ID：</strong></td>
                                        <td>{$notice.id}</td>
                                    </tr>
                                    <tr>
                                        <td class="text-right"><strong>公告标题：</strong></td>
                                        <td>{$notice.title}</td>
                                    </tr>
                                    <tr>
                                        <td class="text-right"><strong>招聘单位：</strong></td>
                                        <td>{$notice.company_name}</td>
                                    </tr>
                                    <tr>
                                        <td class="text-right"><strong>创建时间：</strong></td>
                                        <td>{$notice.create_time_format}</td>
                                    </tr>
                                    <tr>
                                        <td class="text-right"><strong>更新时间：</strong></td>
                                        <td>{$notice.update_time_format|default='未更新'}</td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <div class="well">
                                    <h5><strong>公告描述：</strong></h5>
                                    <p>{$notice.description|default='暂无描述'|nl2br}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 匹配统计 -->
                <div class="panel panel-info">
                    <div class="panel-heading">
                        <h3 class="panel-title">
                            <i class="fa fa-bar-chart"></i> 匹配统计
                            <div class="pull-right">
                                <a href="{:U('resumematch/do_match', array('notice_id'=>$notice['id']))}" class="btn btn-xs btn-primary" onclick="return confirm('确定要重新执行匹配吗？这将清除现有匹配结果。')">
                                    <i class="fa fa-refresh"></i> 重新匹配
                                </a>
                            </div>
                        </h3>
                    </div>
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="stat-box text-center">
                                    <div class="stat-number text-primary">{$match_stats.total_matches}</div>
                                    <div class="stat-label">总匹配数</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stat-box text-center">
                                    <div class="stat-number text-success">{$match_stats.qualified_matches}</div>
                                    <div class="stat-label">符合要求</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stat-box text-center">
                                    <div class="stat-number text-warning">{$match_stats.unqualified_matches}</div>
                                    <div class="stat-label">不符合要求</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stat-box text-center">
                                    <div class="stat-number text-info">{$match_stats.avg_score}</div>
                                    <div class="stat-label">平均分数</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 关联岗位 -->
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h3 class="panel-title">
                            <i class="fa fa-tasks"></i> 关联岗位 ({$notice.post_count})
                            <div class="pull-right">
                                <a href="{:U('recruitmentnotice/posts', array('id'=>$notice['id']))}" class="btn btn-xs btn-warning">
                                    <i class="fa fa-edit"></i> 管理岗位
                                </a>
                            </div>
                        </h3>
                    </div>
                    <div class="panel-body">
                        <php>if(empty($notice['posts'])){</php>
                        <div class="text-center text-muted">
                            <p>暂未关联任何岗位</p>
                            <a href="{:U('recruitmentnotice/posts', array('id'=>$notice['id']))}" class="btn btn-primary">
                                <i class="fa fa-plus"></i> 添加岗位
                            </a>
                        </div>
                        <php>}else{</php>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>岗位名称</th>
                                        <th>所属项目</th>
                                        <th>服务价格</th>
                                        <th>学历要求</th>
                                        <th>性别要求</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <php>foreach($notice['posts'] as $post){</php>
                                    <tr>
                                        <td>{$post.job_name}</td>
                                        <td>{$post.project_name}</td>
                                        <td>{$post.service_price}</td>
                                        <td>
                                            <php>
                                            $qualifications = ['0'=>'不限学历','1'=>'中专及以上','2'=>'大专及以上','3'=>'本科及以上','4'=>'研究生及以上','5'=>'硕士'];
                                            echo $qualifications[$post['qualification']];
                                            </php>
                                        </td>
                                        <td>
                                            <php>
                                            $sexes = ['0'=>'不限性别','1'=>'男','2'=>'女'];
                                            echo $sexes[$post['sex']];
                                            </php>
                                        </td>
                                        <td>
                                            <div class="btn-group">
                                                <a href="{:U('postrequirements/edit', array('notice_id'=>$notice['id'], 'post_id'=>$post['post_id']))}" class="btn btn-xs btn-success" title="配置要求">
                                                    <i class="fa fa-cog"></i>
                                                </a>
                                                <a href="{:U('resumematch/post_matches', array('notice_id'=>$notice['id'], 'post_id'=>$post['post_id']))}" class="btn btn-xs btn-info" title="查看匹配">
                                                    <i class="fa fa-list"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    <php>}</php>
                                </tbody>
                            </table>
                        </div>
                        <php>}</php>
                    </div>
                </div>

                <!-- 岗位要求配置 -->
                <php>if(!empty($requirements)){</php>
                <div class="panel panel-success">
                    <div class="panel-heading">
                        <h3 class="panel-title">
                            <i class="fa fa-cog"></i> 岗位要求配置
                            <div class="pull-right">
                                <a href="{:U('postrequirements/index', array('notice_id'=>$notice['id']))}" class="btn btn-xs btn-success">
                                    <i class="fa fa-edit"></i> 管理配置
                                </a>
                            </div>
                        </h3>
                    </div>
                    <div class="panel-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>岗位名称</th>
                                        <th>年龄要求</th>
                                        <th>性别要求</th>
                                        <th>身高要求</th>
                                        <th>学历要求</th>
                                        <th>工作经验</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <php>foreach($requirements as $req){</php>
                                    <tr>
                                        <td>{$req.job_name}</td>
                                        <td>{$req.age_range|default='不限'}</td>
                                        <td><span class="label label-{$req.gender_info.style}">{$req.gender_info.text}</span></td>
                                        <td>{$req.height_range|default='不限'}</td>
                                        <td><span class="label label-{$req.education_level_info.style}">{$req.education_level_info.text}</span></td>
                                        <td>{$req.work_years_range|default='不限'}</td>
                                        <td>
                                            <div class="btn-group">
                                                <a href="{:U('postrequirements/edit', array('notice_id'=>$notice['id'], 'post_id'=>$req['post_id']))}" class="btn btn-xs btn-primary" title="编辑">
                                                    <i class="fa fa-edit"></i>
                                                </a>
                                                <a href="{:U('postrequirements/preview', array('notice_id'=>$notice['id'], 'post_id'=>$req['post_id']))}" class="btn btn-xs btn-info" title="预览筛选">
                                                    <i class="fa fa-eye"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    <php>}</php>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <php>}</php>

                <!-- 操作按钮 -->
                <div class="form-group">
                    <a href="{:U('recruitmentnotice/index')}" class="btn btn-default">
                        <i class="fa fa-arrow-left"></i> 返回列表
                    </a>
                    <a href="{:U('recruitmentnotice/edit', array('id'=>$notice['id']))}" class="btn btn-primary">
                        <i class="fa fa-edit"></i> 编辑公告
                    </a>
                    <a href="{:U('resumematch/index', array('notice_id'=>$notice['id']))}" class="btn btn-success">
                        <i class="fa fa-list"></i> 查看匹配结果
                    </a>
                    <php>if($match_stats['total_matches'] > 0){</php>
                    <a href="{:U('resumematch/export', array('notice_id'=>$notice['id']))}" class="btn btn-info">
                        <i class="fa fa-download"></i> 导出结果
                    </a>
                    <php>}</php>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.stat-box {
    padding: 20px;
    border: 1px solid #e7e7e7;
    border-radius: 4px;
    margin-bottom: 20px;
}
.stat-number {
    font-size: 36px;
    font-weight: bold;
    margin-bottom: 5px;
}
.stat-label {
    font-size: 14px;
    color: #666;
}
</style>

<include file="block/footer" />
