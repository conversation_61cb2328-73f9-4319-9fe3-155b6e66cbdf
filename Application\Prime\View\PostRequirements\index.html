<include file="block/hat" />
<div class="container-fluid">
    <div class="row">
        <include file="block/menu" />
        <div class="col-xs-12 col-sm-9 col-lg-10">
            <ul class="nav nav-tabs">
                <li class="active"><a>岗位要求配置</a></li>
            </ul>
            <style type='text/css'>.tab-pane {padding:20px 0 20px 0;}</style>
            
            <div class="main">
                <!-- 招聘公告信息 -->
                <div class="panel panel-info">
                    <div class="panel-heading">
                        <h3 class="panel-title">
                            <i class="fa fa-info-circle"></i> 招聘公告信息
                        </h3>
                    </div>
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-8">
                                <h4>{$notice.title}</h4>
                                <p class="text-muted">招聘单位：{$notice.company_name}</p>
                                <php>if($notice['description']){</php>
                                <p>{$notice.description}</p>
                                <php>}</php>
                            </div>
                            <div class="col-md-4 text-right">
                                <a href="{:U('recruitmentnotice/detail', array('id'=>$notice['id']))}" class="btn btn-default">
                                    <i class="fa fa-arrow-left"></i> 返回详情
                                </a>
                                <a href="{:U('postrequirements/batch_config', array('notice_id'=>$notice['id']))}" class="btn btn-success">
                                    <i class="fa fa-cogs"></i> 批量配置
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 岗位列表 -->
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h3 class="panel-title">
                            <i class="fa fa-tasks"></i> 岗位要求配置 ({$posts|count})
                        </h3>
                    </div>
                    <div class="panel-body">
                        <php>if(empty($posts)){</php>
                        <div class="text-center text-muted">
                            <p>该招聘公告暂未关联任何岗位</p>
                            <a href="{:U('recruitmentnotice/posts', array('id'=>$notice['id']))}" class="btn btn-primary">
                                <i class="fa fa-plus"></i> 添加岗位
                            </a>
                        </div>
                        <php>}else{</php>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="navbar-inner">
                                    <tr>
                                        <th>岗位名称</th>
                                        <th>所属项目</th>
                                        <th>服务价格</th>
                                        <th>配置状态</th>
                                        <th>年龄要求</th>
                                        <th>性别要求</th>
                                        <th>身高要求</th>
                                        <th>学历要求</th>
                                        <th width="150">操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <php>foreach($posts as $post){</php>
                                    <php>$req = isset($requirements_map[$post['post_id']]) ? $requirements_map[$post['post_id']] : null;</php>
                                    <tr>
                                        <td>
                                            <strong>{$post.job_name}</strong>
                                        </td>
                                        <td>{$post.project_name}</td>
                                        <td>{$post.service_price}</td>
                                        <td>
                                            <php>if($req){</php>
                                            <span class="label label-success">已配置</span>
                                            <php>}else{</php>
                                            <span class="label label-warning">未配置</span>
                                            <php>}</php>
                                        </td>
                                        <td>
                                            <php>if($req){</php>
                                            {$req.age_range|default='不限'}
                                            <php>}else{</php>
                                            <span class="text-muted">-</span>
                                            <php>}</php>
                                        </td>
                                        <td>
                                            <php>if($req){</php>
                                            <span class="label label-{$req.gender_info.style}">{$req.gender_info.text}</span>
                                            <php>}else{</php>
                                            <span class="text-muted">-</span>
                                            <php>}</php>
                                        </td>
                                        <td>
                                            <php>if($req){</php>
                                            {$req.height_range|default='不限'}
                                            <php>}else{</php>
                                            <span class="text-muted">-</span>
                                            <php>}</php>
                                        </td>
                                        <td>
                                            <php>if($req){</php>
                                            <span class="label label-{$req.education_level_info.style}">{$req.education_level_info.text}</span>
                                            <php>}else{</php>
                                            <span class="text-muted">-</span>
                                            <php>}</php>
                                        </td>
                                        <td>
                                            <div class="btn-group">
                                                <a href="{:U('postrequirements/edit', array('notice_id'=>$notice['id'], 'post_id'=>$post['post_id']))}" class="btn btn-xs btn-primary" title="配置要求">
                                                    <i class="fa fa-cog"></i>
                                                </a>
                                                <php>if($req){</php>
                                                <a href="{:U('postrequirements/preview', array('notice_id'=>$notice['id'], 'post_id'=>$post['post_id']))}" class="btn btn-xs btn-info" title="预览筛选" target="_blank">
                                                    <i class="fa fa-eye"></i>
                                                </a>
                                                <a href="{:U('resumematch/post_matches', array('notice_id'=>$notice['id'], 'post_id'=>$post['post_id']))}" class="btn btn-xs btn-success" title="查看匹配">
                                                    <i class="fa fa-list"></i>
                                                </a>
                                                <a href="{:U('postrequirements/delete', array('notice_id'=>$notice['id'], 'post_id'=>$post['post_id']))}" class="btn btn-xs btn-danger js_ajax" confirm="确定要删除这个岗位的要求配置吗？" title="删除配置">
                                                    <i class="fa fa-trash"></i>
                                                </a>
                                                <php>}</php>
                                            </div>
                                        </td>
                                    </tr>
                                    <php>}</php>
                                </tbody>
                            </table>
                        </div>
                        <php>}</php>
                    </div>
                </div>

                <!-- 配置统计 -->
                <php>if(!empty($posts)){</php>
                <div class="panel panel-info">
                    <div class="panel-heading">
                        <h3 class="panel-title">
                            <i class="fa fa-bar-chart"></i> 配置统计
                        </h3>
                    </div>
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="stat-box text-center">
                                    <div class="stat-number text-primary">{$posts|count}</div>
                                    <div class="stat-label">总岗位数</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stat-box text-center">
                                    <div class="stat-number text-success">
                                        <php>
                                        $configured = 0;
                                        foreach($posts as $post) {
                                            if(isset($requirements_map[$post['post_id']])) {
                                                $configured++;
                                            }
                                        }
                                        echo $configured;
                                        </php>
                                    </div>
                                    <div class="stat-label">已配置</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stat-box text-center">
                                    <div class="stat-number text-warning">
                                        <php>echo count($posts) - $configured;</php>
                                    </div>
                                    <div class="stat-label">未配置</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stat-box text-center">
                                    <div class="stat-number text-info">
                                        <php>echo count($posts) > 0 ? round($configured / count($posts) * 100, 1) : 0;</php>%
                                    </div>
                                    <div class="stat-label">配置率</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <php>}</php>

                <!-- 操作提示 -->
                <div class="alert alert-info">
                    <h4><i class="fa fa-lightbulb-o"></i> 操作提示</h4>
                    <ul class="mb-0">
                        <li>点击"配置要求"按钮为单个岗位设置筛选条件</li>
                        <li>使用"批量配置"功能可以为多个岗位设置相同的筛选条件</li>
                        <li>配置完成后可以点击"预览筛选"查看符合条件的简历</li>
                        <li>所有岗位配置完成后，可以在招聘公告详情页执行简历匹配</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.stat-box {
    padding: 20px;
    border: 1px solid #e7e7e7;
    border-radius: 4px;
    margin-bottom: 20px;
}
.stat-number {
    font-size: 36px;
    font-weight: bold;
    margin-bottom: 5px;
}
.stat-label {
    font-size: 14px;
    color: #666;
}
</style>

<include file="block/footer" />
