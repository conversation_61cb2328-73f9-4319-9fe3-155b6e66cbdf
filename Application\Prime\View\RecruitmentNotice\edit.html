<include file="block/hat" />
<div class="container-fluid">
    <div class="row">
        <include file="block/menu" />
        <div class="col-xs-12 col-sm-9 col-lg-10">
            <ul class="nav nav-tabs">
                <li class="active">
                    <a><php>echo isset($row) ? '编辑' : '新增';</php>招聘公告</a>
                </li>
            </ul>
            <style type='text/css'>.tab-pane {padding:20px 0 20px 0;}</style>
            
            <div class="main">
                <form action="{:U('recruitmentnotice/edit')}" method="post" class="form-horizontal js-ajax-form" enctype="multipart/form-data">
                    <php>if(isset($row)){</php>
                    <input type="hidden" name="id" value="{$row.id}" />
                    <php>}</php>
                    
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h3 class="panel-title">基本信息</h3>
                        </div>
                        <div class="panel-body">
                            <div class="form-group">
                                <label class="col-sm-2 control-label"><span class="text-danger">*</span>公告标题</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" name="title" value="{$row.title|default=''}" placeholder="请输入招聘公告标题" required>
                                    <span class="help-block">请输入简洁明了的招聘公告标题</span>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label class="col-sm-2 control-label"><span class="text-danger">*</span>招聘单位</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" name="company_name" value="{$row.company_name|default=''}" placeholder="请输入招聘单位名称" required>
                                    <span class="help-block">请输入完整的招聘单位名称</span>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label class="col-sm-2 control-label">公告描述</label>
                                <div class="col-sm-8">
                                    <textarea class="form-control" name="description" rows="6" placeholder="请输入招聘公告的详细描述">{$row.description|default=''}</textarea>
                                    <span class="help-block">可以包含招聘背景、福利待遇、联系方式等信息</span>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label class="col-sm-2 control-label">状态</label>
                                <div class="col-sm-8">
                                    <php>foreach($status_options as $key=>$value){</php>
                                    <label class="radio-inline">
                                        <input type="radio" name="status" value="{$key}" <php>if((isset($row) && $row['status'] == $key) || (!isset($row) && $key == 1)){</php>checked<php>}</php>>
                                        {$value.text}
                                    </label>
                                    <php>}</php>
                                    <span class="help-block">启用状态的公告才能进行简历匹配</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <div class="col-sm-offset-2 col-sm-8">
                            <button type="submit" class="btn btn-primary">
                                <i class="fa fa-save"></i> 保存
                            </button>
                            <a href="{:U('recruitmentnotice/index')}" class="btn btn-default">
                                <i class="fa fa-arrow-left"></i> 返回列表
                            </a>
                            <php>if(isset($row)){</php>
                            <a href="{:U('recruitmentnotice/posts', array('id'=>$row['id']))}" class="btn btn-warning">
                                <i class="fa fa-tasks"></i> 管理岗位
                            </a>
                            <a href="{:U('postrequirements/index', array('notice_id'=>$row['id']))}" class="btn btn-success">
                                <i class="fa fa-cog"></i> 配置要求
                            </a>
                            <php>}</php>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
$(function(){
    // 表单验证
    $('.js-ajax-form').on('submit', function(e) {
        var title = $('input[name="title"]').val().trim();
        var company_name = $('input[name="company_name"]').val().trim();
        
        if (!title) {
            layer.msg('请输入公告标题', {icon: 2});
            $('input[name="title"]').focus();
            return false;
        }
        
        if (!company_name) {
            layer.msg('请输入招聘单位', {icon: 2});
            $('input[name="company_name"]').focus();
            return false;
        }
        
        // 显示保存状态
        var $submitBtn = $(this).find('button[type="submit"]');
        var originalText = $submitBtn.html();
        $submitBtn.html('<i class="fa fa-spinner fa-spin"></i> 保存中...');
        $submitBtn.prop('disabled', true);
        
        // 恢复按钮状态（如果提交失败）
        setTimeout(function() {
            $submitBtn.html(originalText);
            $submitBtn.prop('disabled', false);
        }, 3000);
    });
});
</script>

<include file="block/footer" />
