<include file="block/hat" />
<style>
/* 表单页面样式 */
.form-header {
    background: linear-gradient(135deg, #fd7e14 0%, #e83e8c 100%);
    color: white;
    padding: 20px;
    border-radius: 8px 8px 0 0;
    margin-bottom: 0;
}

.form-header h4 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.form-container {
    background: white;
    border-radius: 0 0 8px 8px;
    padding: 30px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.form-group {
    margin-bottom: 25px;
}

.form-group label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
}

.form-control {
    border: 2px solid #e9ecef;
    border-radius: 6px;
    padding: 12px 15px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #fd7e14;
    box-shadow: 0 0 0 3px rgba(253, 126, 20, 0.1);
    outline: none;
}

.help-block {
    color: #6c757d;
    font-size: 12px;
    margin-top: 5px;
    font-style: italic;
}

.required-mark {
    color: #dc3545;
    font-weight: bold;
}

.radio {
    margin-bottom: 10px;
}

.radio label {
    font-weight: normal;
    color: #495057;
    cursor: pointer;
    padding-left: 25px;
}

.radio input[type="radio"] {
    margin-left: -25px;
    margin-right: 8px;
}

.btn-primary {
    background: linear-gradient(135deg, #fd7e14 0%, #e83e8c 100%);
    border: none;
    padding: 12px 30px;
    font-weight: 600;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #e8690b 0%, #d91a72 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(253, 126, 20, 0.3);
}

.btn-default {
    border: 2px solid #e9ecef;
    padding: 12px 30px;
    font-weight: 600;
    border-radius: 6px;
    transition: all 0.3s ease;
    background: white;
    color: #495057;
}

.btn-default:hover {
    border-color: #fd7e14;
    color: #fd7e14;
    background: rgba(253, 126, 20, 0.05);
}

.form-actions {
    border-top: 1px solid #e9ecef;
    padding-top: 25px;
    margin-top: 30px;
}

.edit-info {
    background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
    border: 1px solid #bbdefb;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 25px;
}

.edit-info h6 {
    margin: 0 0 10px 0;
    color: #1976d2;
    font-weight: 600;
}

.edit-info .info-item {
    margin-bottom: 5px;
    font-size: 13px;
    color: #424242;
}
</style>

<div class="container-fluid">
    <div class="row">
        <include file="block/menu" />
        <div class="col-xs-12 col-sm-9 col-lg-10">
            <div class="main">
                <div class="panel panel-default" style="border: none; box-shadow: 0 4px 20px rgba(0,0,0,0.1); border-radius: 8px;">
                    <div class="form-header">
                        <h4>
                            <i class="fa fa-edit"></i> 编辑招聘公告
                            <small style="opacity: 0.8; margin-left: 10px;">修改公告信息</small>
                        </h4>
                    </div>
                    <div class="form-container">
                        <!-- 编辑信息提示 -->
                        <div class="edit-info">
                            <h6><i class="fa fa-info-circle"></i> 编辑信息</h6>
                            <div class="info-item"><strong>公告ID：</strong>#{$info.id}</div>
                            <div class="info-item"><strong>创建时间：</strong>{$info.create_time|date='Y-m-d H:i:s',###}</div>
                            <div class="info-item"><strong>最后更新：</strong>{$info.update_time|date='Y-m-d H:i:s',###}</div>
                        </div>

                        <form class="form-horizontal" method="post" action="" id="editNoticeForm">
                            <input type="hidden" name="id" value="{$info.id}">

                            <div class="form-group">
                                <label class="col-sm-3 control-label">
                                    <i class="fa fa-bullhorn"></i> 公告标题
                                    <span class="required-mark">*</span>
                                </label>
                                <div class="col-sm-8">
                                    <input type="text" name="title" class="form-control" value="{$info.title}" placeholder="请输入招聘公告标题" required maxlength="255">
                                    <span class="help-block">
                                        <i class="fa fa-info-circle"></i> 公告的标题，用于标识和搜索，建议简洁明了
                                    </span>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-sm-3 control-label">
                                    <i class="fa fa-building"></i> 招聘单位
                                </label>
                                <div class="col-sm-8">
                                    <input type="text" name="company_name" class="form-control" value="{$info.company_name}" placeholder="请输入招聘单位名称" maxlength="255">
                                    <span class="help-block">
                                        <i class="fa fa-info-circle"></i> 发布招聘的单位或公司名称，可选填
                                    </span>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-sm-3 control-label">
                                    <i class="fa fa-file-text"></i> 公告描述
                                </label>
                                <div class="col-sm-8">
                                    <textarea name="description" class="form-control" rows="6" placeholder="请详细描述招聘公告的内容和要求...">{$info.description}</textarea>
                                    <span class="help-block">
                                        <i class="fa fa-info-circle"></i> 详细描述招聘公告的内容和要求，支持换行
                                    </span>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-sm-3 control-label">
                                    <i class="fa fa-toggle-on"></i> 发布状态
                                </label>
                                <div class="col-sm-8">
                                    <div class="radio">
                                        <label style="color: #28a745; font-weight: 500;">
                                            <input type="radio" name="status" value="1" <if condition="$info.status eq 1">checked</if>>
                                            <i class="fa fa-check-circle"></i> 启用
                                            <small style="color: #6c757d; margin-left: 10px;">启用状态，可进行岗位配置和简历筛选</small>
                                        </label>
                                    </div>
                                    <div class="radio">
                                        <label style="color: #6c757d; font-weight: 500;">
                                            <input type="radio" name="status" value="0" <if condition="$info.status eq 0">checked</if>>
                                            <i class="fa fa-pause-circle"></i> 停用
                                            <small style="color: #6c757d; margin-left: 10px;">停用状态，暂停相关功能</small>
                                        </label>
                                    </div>
                                    <span class="help-block">
                                        <i class="fa fa-info-circle"></i> 启用后可以进行岗位配置和简历筛选功能
                                    </span>
                                </div>
                            </div>

                            <div class="form-actions">
                                <div class="col-sm-offset-3 col-sm-8">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fa fa-save"></i> 保存修改
                                    </button>
                                    <a href="{:U('index')}" class="btn btn-default" style="margin-left: 10px;">
                                        <i class="fa fa-arrow-left"></i> 返回列表
                                    </a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // 表单验证
    $('#editNoticeForm').on('submit', function(e) {
        var title = $('input[name="title"]').val().trim();

        if (!title) {
            alert('❌ 请输入公告标题');
            $('input[name="title"]').focus();
            return false;
        }

        if (title.length < 2) {
            alert('❌ 公告标题至少需要2个字符');
            $('input[name="title"]').focus();
            return false;
        }

        // 显示提交状态
        $(this).find('button[type="submit"]').html('<i class="fa fa-spinner fa-spin"></i> 保存中...').prop('disabled', true);

        return true;
    });

    // 输入框焦点效果
    $('.form-control').on('focus', function() {
        $(this).closest('.form-group').addClass('focused');
    }).on('blur', function() {
        $(this).closest('.form-group').removeClass('focused');
    });

    // 字符计数
    $('input[name="title"]').on('input', function() {
        var length = $(this).val().length;
        var maxLength = 255;
        var $helpBlock = $(this).siblings('.help-block');
        var originalText = '<i class="fa fa-info-circle"></i> 公告的标题，用于标识和搜索，建议简洁明了';

        if (length > 0) {
            $helpBlock.html(originalText + ' <span style="color: #fd7e14;">(' + length + '/' + maxLength + ')</span>');
        } else {
            $helpBlock.html(originalText);
        }
    });

    // 描述字符计数
    $('textarea[name="description"]').on('input', function() {
        var length = $(this).val().length;
        var $helpBlock = $(this).siblings('.help-block');
        var originalText = '<i class="fa fa-info-circle"></i> 详细描述招聘公告的内容和要求，支持换行';

        if (length > 0) {
            $helpBlock.html(originalText + ' <span style="color: #fd7e14;">(' + length + ' 字符)</span>');
        } else {
            $helpBlock.html(originalText);
        }
    });

    // 单选按钮美化
    $('input[type="radio"]').on('change', function() {
        $('input[type="radio"][name="' + $(this).attr('name') + '"]').closest('label').removeClass('selected');
        $(this).closest('label').addClass('selected');
    });

    // 初始化选中状态
    $('input[type="radio"]:checked').closest('label').addClass('selected');

    // 初始化字符计数
    $('input[name="title"]').trigger('input');
    $('textarea[name="description"]').trigger('input');

    // 编辑提示动画
    $('.edit-info').hide().fadeIn(800);
});
</script>

<style>
.form-group.focused .form-control {
    border-color: #fd7e14;
    box-shadow: 0 0 0 3px rgba(253, 126, 20, 0.1);
}

.radio label.selected {
    background: rgba(253, 126, 20, 0.1);
    border-radius: 6px;
    padding: 10px 15px;
    margin-bottom: 5px;
    transition: all 0.3s ease;
}

.form-group {
    position: relative;
    overflow: hidden;
}

.form-group::before {
    content: '';
    position: absolute;
    left: 0;
    bottom: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, #fd7e14, #e83e8c);
    transition: width 0.3s ease;
}

.form-group.focused::before {
    width: 100%;
}

.edit-info {
    animation: slideInDown 0.8s ease;
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>

<include file="block/footer" />
