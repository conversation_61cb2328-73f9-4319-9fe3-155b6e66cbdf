<include file="block/hat" />
<div class="container-fluid">
    <div class="row">
        <include file="block/menu" />
        <div class="col-xs-12 col-sm-9 col-lg-10">
            <ul class="nav nav-tabs">
                <li class="active"><a>编辑招聘公告</a></li>
            </ul>
            <style type='text/css'>.tab-pane {padding:20px 0 20px 0;}</style>
            <div class="main">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <i class="fa fa-edit"></i> 编辑招聘公告
                        </h4>
                    </div>
                    <div class="panel-body">
                        <form class="form-horizontal" method="post" action="">
                            <input type="hidden" name="id" value="{$info.id}">
                            
                            <div class="form-group">
                                <label class="col-sm-2 control-label">公告标题 <span class="text-danger">*</span></label>
                                <div class="col-sm-8">
                                    <input type="text" name="title" class="form-control" value="{$info.title}" placeholder="请输入公告标题" required>
                                    <span class="help-block">公告的标题，用于标识和搜索</span>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label class="col-sm-2 control-label">招聘单位</label>
                                <div class="col-sm-8">
                                    <input type="text" name="company_name" class="form-control" value="{$info.company_name}" placeholder="请输入招聘单位名称">
                                    <span class="help-block">发布招聘的单位或公司名称</span>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label class="col-sm-2 control-label">公告描述</label>
                                <div class="col-sm-8">
                                    <textarea name="description" class="form-control" rows="5" placeholder="请输入公告描述">{$info.description}</textarea>
                                    <span class="help-block">详细描述招聘公告的内容和要求</span>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label class="col-sm-2 control-label">状态</label>
                                <div class="col-sm-8">
                                    <div class="radio">
                                        <label>
                                            <input type="radio" name="status" value="1" <if condition="$info.status eq 1">checked</if>> 启用
                                        </label>
                                    </div>
                                    <div class="radio">
                                        <label>
                                            <input type="radio" name="status" value="0" <if condition="$info.status eq 0">checked</if>> 停用
                                        </label>
                                    </div>
                                    <span class="help-block">启用后可以进行岗位配置和简历筛选</span>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <div class="col-sm-offset-2 col-sm-8">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fa fa-save"></i> 保存
                                    </button>
                                    <a href="{:U('index')}" class="btn btn-default">
                                        <i class="fa fa-arrow-left"></i> 返回
                                    </a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<include file="block/footer" />
