<include file="block/hat" />
<style>
/* 表单页面样式 */
.form-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 8px 8px 0 0;
    margin-bottom: 0;
}

.form-header h4 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.form-container {
    background: white;
    border-radius: 0 0 8px 8px;
    padding: 30px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.form-group {
    margin-bottom: 25px;
}

.form-group label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
}

.form-control {
    border: 2px solid #e9ecef;
    border-radius: 6px;
    padding: 12px 15px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    outline: none;
}

.help-block {
    color: #6c757d;
    font-size: 12px;
    margin-top: 5px;
    font-style: italic;
}

.required-mark {
    color: #dc3545;
    font-weight: bold;
}

.radio {
    margin-bottom: 10px;
}

.radio label {
    font-weight: normal;
    color: #495057;
    cursor: pointer;
    padding-left: 25px;
}

.radio input[type="radio"] {
    margin-left: -25px;
    margin-right: 8px;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    padding: 12px 30px;
    font-weight: 600;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a67d8 0%, #667eea 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.btn-default {
    border: 2px solid #e9ecef;
    padding: 12px 30px;
    font-weight: 600;
    border-radius: 6px;
    transition: all 0.3s ease;
    background: white;
    color: #495057;
}

.btn-default:hover {
    border-color: #667eea;
    color: #667eea;
    background: rgba(102, 126, 234, 0.05);
}

.form-actions {
    border-top: 1px solid #e9ecef;
    padding-top: 25px;
    margin-top: 30px;
}
</style>

<div class="container-fluid">
    <div class="row">
        <include file="block/menu" />
        <div class="col-xs-12 col-sm-9 col-lg-10">
            <div class="main">
                <div class="panel panel-default" style="border: none; box-shadow: 0 4px 20px rgba(0,0,0,0.1); border-radius: 8px;">
                    <div class="form-header">
                        <h4>
                            <i class="fa fa-plus-circle"></i> 添加招聘公告
                            <small style="opacity: 0.8; margin-left: 10px;">创建新的招聘公告</small>
                        </h4>
                    </div>
                    <div class="form-container">
                        <form class="form-horizontal" method="post" action="" id="addNoticeForm">
                            <div class="form-group">
                                <label class="col-sm-3 control-label">
                                    <i class="fa fa-bullhorn"></i> 公告标题
                                    <span class="required-mark">*</span>
                                </label>
                                <div class="col-sm-8">
                                    <input type="text" name="title" class="form-control" placeholder="请输入招聘公告标题，如：2024年春季校园招聘" required maxlength="255">
                                    <span class="help-block">
                                        <i class="fa fa-info-circle"></i> 公告的标题，用于标识和搜索，建议简洁明了
                                    </span>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-sm-3 control-label">
                                    <i class="fa fa-building"></i> 招聘单位
                                </label>
                                <div class="col-sm-8">
                                    <input type="text" name="company_name" class="form-control" placeholder="请输入招聘单位名称，如：某某科技有限公司" maxlength="255">
                                    <span class="help-block">
                                        <i class="fa fa-info-circle"></i> 发布招聘的单位或公司名称，可选填
                                    </span>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-sm-3 control-label">
                                    <i class="fa fa-file-text"></i> 公告描述
                                </label>
                                <div class="col-sm-8">
                                    <textarea name="description" class="form-control" rows="6" placeholder="请详细描述招聘公告的内容和要求，如招聘背景、基本要求、福利待遇等..."></textarea>
                                    <span class="help-block">
                                        <i class="fa fa-info-circle"></i> 详细描述招聘公告的内容和要求，支持换行
                                    </span>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-sm-3 control-label">
                                    <i class="fa fa-toggle-on"></i> 发布状态
                                </label>
                                <div class="col-sm-8">
                                    <div class="radio">
                                        <label style="color: #28a745; font-weight: 500;">
                                            <input type="radio" name="status" value="1" checked>
                                            <i class="fa fa-check-circle"></i> 启用
                                            <small style="color: #6c757d; margin-left: 10px;">立即启用，可进行岗位配置和简历筛选</small>
                                        </label>
                                    </div>
                                    <div class="radio">
                                        <label style="color: #6c757d; font-weight: 500;">
                                            <input type="radio" name="status" value="0">
                                            <i class="fa fa-pause-circle"></i> 停用
                                            <small style="color: #6c757d; margin-left: 10px;">暂时停用，稍后可手动启用</small>
                                        </label>
                                    </div>
                                    <span class="help-block">
                                        <i class="fa fa-info-circle"></i> 启用后可以进行岗位配置和简历筛选功能
                                    </span>
                                </div>
                            </div>

                            <div class="form-actions">
                                <div class="col-sm-offset-3 col-sm-8">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fa fa-save"></i> 保存公告
                                    </button>
                                    <a href="{:U('index')}" class="btn btn-default" style="margin-left: 10px;">
                                        <i class="fa fa-arrow-left"></i> 返回列表
                                    </a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // 表单验证
    $('#addNoticeForm').on('submit', function(e) {
        var title = $('input[name="title"]').val().trim();

        if (!title) {
            alert('❌ 请输入公告标题');
            $('input[name="title"]').focus();
            return false;
        }

        if (title.length < 2) {
            alert('❌ 公告标题至少需要2个字符');
            $('input[name="title"]').focus();
            return false;
        }

        // 显示提交状态
        $(this).find('button[type="submit"]').html('<i class="fa fa-spinner fa-spin"></i> 保存中...').prop('disabled', true);

        return true;
    });

    // 输入框焦点效果
    $('.form-control').on('focus', function() {
        $(this).closest('.form-group').addClass('focused');
    }).on('blur', function() {
        $(this).closest('.form-group').removeClass('focused');
    });

    // 字符计数
    $('input[name="title"]').on('input', function() {
        var length = $(this).val().length;
        var maxLength = 255;
        var $helpBlock = $(this).siblings('.help-block');
        var originalText = $helpBlock.html();

        if (length > 0) {
            $helpBlock.html(originalText + ' <span style="color: #667eea;">(' + length + '/' + maxLength + ')</span>');
        } else {
            $helpBlock.html(originalText);
        }
    });

    // 描述字符计数
    $('textarea[name="description"]').on('input', function() {
        var length = $(this).val().length;
        var $helpBlock = $(this).siblings('.help-block');
        var originalText = '<i class="fa fa-info-circle"></i> 详细描述招聘公告的内容和要求，支持换行';

        if (length > 0) {
            $helpBlock.html(originalText + ' <span style="color: #667eea;">(' + length + ' 字符)</span>');
        } else {
            $helpBlock.html(originalText);
        }
    });

    // 单选按钮美化
    $('input[type="radio"]').on('change', function() {
        $('input[type="radio"][name="' + $(this).attr('name') + '"]').closest('label').removeClass('selected');
        $(this).closest('label').addClass('selected');
    });

    // 初始化选中状态
    $('input[type="radio"]:checked').closest('label').addClass('selected');
});
</script>

<style>
.form-group.focused .form-control {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.radio label.selected {
    background: rgba(102, 126, 234, 0.1);
    border-radius: 6px;
    padding: 10px 15px;
    margin-bottom: 5px;
    transition: all 0.3s ease;
}

.form-group {
    position: relative;
    overflow: hidden;
}

.form-group::before {
    content: '';
    position: absolute;
    left: 0;
    bottom: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    transition: width 0.3s ease;
}

.form-group.focused::before {
    width: 100%;
}
</style>

<include file="block/footer" />
