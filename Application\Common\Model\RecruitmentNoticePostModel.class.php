<?php
namespace Common\Model;

use Think\Model;

/**
 * 招聘公告岗位关联模型
 */
class RecruitmentNoticePostModel extends Model
{
    protected $_validate = [
        ['notice_id', 'require', '招聘公告ID不能为空'],
        ['post_id', 'require', '岗位ID不能为空'],
    ];

    protected $_auto = [
        ['create_time', 'time', self::MODEL_INSERT, 'function'],
    ];

    /**
     * 获取公告关联的岗位列表
     */
    public function getPostsByNotice($notice_id)
    {
        return $this->alias('rnp')
            ->join('LEFT JOIN __PROJECT_POST__ pp ON rnp.post_id = pp.id')
            ->join('LEFT JOIN __PROJECT__ p ON pp.project_id = p.id')
            ->where(['rnp.notice_id' => $notice_id])
            ->field('pp.*, p.name as project_name')
            ->select();
    }

    /**
     * 获取岗位关联的公告列表
     */
    public function getNoticesByPost($post_id)
    {
        return $this->alias('rnp')
            ->join('LEFT JOIN __RECRUITMENT_NOTICE__ rn ON rnp.notice_id = rn.id')
            ->where(['rnp.post_id' => $post_id])
            ->field('rn.*')
            ->select();
    }

    /**
     * 批量添加岗位关联
     */
    public function addPostsToNotice($notice_id, $post_ids)
    {
        if (empty($post_ids)) {
            return false;
        }

        // 先删除原有关联
        $this->where(['notice_id' => $notice_id])->delete();

        // 添加新关联
        $data = [];
        foreach ($post_ids as $post_id) {
            $data[] = [
                'notice_id' => $notice_id,
                'post_id' => $post_id,
                'create_time' => time()
            ];
        }

        return $this->addAll($data);
    }

    /**
     * 删除公告的所有岗位关联
     */
    public function deleteByNotice($notice_id)
    {
        return $this->where(['notice_id' => $notice_id])->delete();
    }

    /**
     * 删除岗位的所有公告关联
     */
    public function deleteByPost($post_id)
    {
        return $this->where(['post_id' => $post_id])->delete();
    }

    /**
     * 检查岗位是否已关联到公告
     */
    public function isPostLinked($notice_id, $post_id)
    {
        return $this->where(['notice_id' => $notice_id, 'post_id' => $post_id])->count() > 0;
    }
}
