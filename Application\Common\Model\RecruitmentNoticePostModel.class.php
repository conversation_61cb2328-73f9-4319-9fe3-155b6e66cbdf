<?php
namespace Common\Model;

use Common\Model\CommonModel;

/**
 * 招聘公告岗位关联模型
 */
class RecruitmentNoticePostModel extends CommonModel
{
    protected $_auto = [
        ['create_time', 'time', self::MODEL_INSERT, 'function'],
    ];

    /**
     * 获取招聘公告的关联岗位
     * @param int $noticeId 公告ID
     * @return array
     */
    public function getNoticePostIds($noticeId)
    {
        return $this->where(['notice_id' => $noticeId])->getField('post_id', true);
    }

    /**
     * 获取岗位关联的招聘公告
     * @param int $postId 岗位ID
     * @return array
     */
    public function getPostNoticeIds($postId)
    {
        return $this->where(['post_id' => $postId])->getField('notice_id', true);
    }

    /**
     * 批量添加岗位关联
     * @param int $noticeId 公告ID
     * @param array $postIds 岗位ID数组
     * @return bool
     */
    public function batchAddPosts($noticeId, $postIds)
    {
        if (empty($postIds)) {
            return true;
        }

        // 先删除原有关联
        $this->where(['notice_id' => $noticeId])->delete();

        // 添加新关联
        $data = [];
        foreach ($postIds as $postId) {
            $data[] = [
                'notice_id' => $noticeId,
                'post_id' => $postId,
                'create_time' => time()
            ];
        }

        return $this->addAll($data);
    }

    /**
     * 删除招聘公告的所有岗位关联
     * @param int $noticeId 公告ID
     * @return bool
     */
    public function deleteNoticeAllPosts($noticeId)
    {
        return $this->where(['notice_id' => $noticeId])->delete();
    }

    /**
     * 删除岗位的所有招聘公告关联
     * @param int $postId 岗位ID
     * @return bool
     */
    public function deletePostAllNotices($postId)
    {
        return $this->where(['post_id' => $postId])->delete();
    }

    /**
     * 检查岗位是否已关联到招聘公告
     * @param int $noticeId 公告ID
     * @param int $postId 岗位ID
     * @return bool
     */
    public function isPostLinked($noticeId, $postId)
    {
        return $this->where([
            'notice_id' => $noticeId,
            'post_id' => $postId
        ])->count() > 0;
    }

    /**
     * 获取招聘公告关联的岗位详情
     * @param int $noticeId 公告ID
     * @return array
     */
    public function getNoticePostsWithDetail($noticeId)
    {
        return $this->alias('rnp')
            ->join('LEFT JOIN __PROJECT_POST__ pp ON rnp.post_id = pp.id')
            ->join('LEFT JOIN __PROJECT__ p ON pp.project_id = p.id')
            ->where(['rnp.notice_id' => $noticeId])
            ->field('rnp.*, pp.job_name, pp.service_price, pp.qualification, pp.sex, pp.min_age, pp.max_age, pp.height, pp.major, p.name as project_name')
            ->order('p.id DESC, pp.id DESC')
            ->select();
    }

    /**
     * 统计招聘公告关联的岗位数量
     * @param int $noticeId 公告ID
     * @return int
     */
    public function countNoticePosts($noticeId)
    {
        return $this->where(['notice_id' => $noticeId])->count();
    }

    /**
     * 统计岗位关联的招聘公告数量
     * @param int $postId 岗位ID
     * @return int
     */
    public function countPostNotices($postId)
    {
        return $this->where(['post_id' => $postId])->count();
    }
}
