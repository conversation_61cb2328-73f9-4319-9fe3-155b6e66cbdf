<include file="block/hat" />
<div class="container-fluid">
    <div class="row">
        <include file="block/menu" />
        <div class="col-xs-12 col-sm-9 col-lg-10">
            <ul class="nav nav-tabs">
                <li class="active"><a>岗位关联管理</a></li>
            </ul>
            <style type='text/css'>.tab-pane {padding:20px 0 20px 0;}</style>
            
            <div class="main">
                <!-- 招聘公告信息 -->
                <div class="panel panel-info">
                    <div class="panel-heading">
                        <h3 class="panel-title">
                            <i class="fa fa-info-circle"></i> 招聘公告信息
                        </h3>
                    </div>
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-8">
                                <h4>{$notice.title}</h4>
                                <p class="text-muted">招聘单位：{$notice.company_name}</p>
                            </div>
                            <div class="col-md-4 text-right">
                                <a href="{:U('recruitmentnotice/detail', array('id'=>$notice['id']))}" class="btn btn-default">
                                    <i class="fa fa-arrow-left"></i> 返回详情
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 岗位选择表单 -->
                <form action="{:U('recruitmentnotice/posts')}" method="post" class="js-ajax-form">
                    <input type="hidden" name="id" value="{$notice.id}" />
                    
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h3 class="panel-title">
                                <i class="fa fa-tasks"></i> 选择关联岗位
                                <div class="pull-right">
                                    <button type="button" class="btn btn-xs btn-success" onclick="selectAll()">
                                        <i class="fa fa-check"></i> 全选
                                    </button>
                                    <button type="button" class="btn btn-xs btn-warning" onclick="selectNone()">
                                        <i class="fa fa-times"></i> 全不选
                                    </button>
                                </div>
                            </h3>
                        </div>
                        <div class="panel-body">
                            <php>if(empty($all_posts)){</php>
                            <div class="text-center text-muted">
                                <p>暂无可用岗位</p>
                                <p>请先在项目管理中创建岗位</p>
                            </div>
                            <php>}else{</php>
                            <div class="row">
                                <php>
                                $current_project = '';
                                foreach($all_posts as $post) {
                                    if($current_project != $post['project_name']) {
                                        if($current_project != '') {
                                            echo '</div></div>';
                                        }
                                        $current_project = $post['project_name'];
                                        echo '<div class="col-md-6"><div class="panel panel-default">';
                                        echo '<div class="panel-heading"><h5 class="panel-title"><i class="fa fa-folder-o"></i> ' . $current_project . '</h5></div>';
                                        echo '<div class="panel-body">';
                                    }
                                </php>
                                    <div class="checkbox">
                                        <label>
                                            <input type="checkbox" name="post_ids[]" value="{$post.id}" 
                                                   <php>if(in_array($post['id'], $linked_posts)){</php>checked<php>}</php>>
                                            <strong>{$post.job_name}</strong>
                                            <php>
                                            // 显示岗位基本信息
                                            $qualifications = ['0'=>'不限学历','1'=>'中专及以上','2'=>'大专及以上','3'=>'本科及以上','4'=>'研究生及以上','5'=>'硕士'];
                                            $sexes = ['0'=>'不限性别','1'=>'男','2'=>'女'];
                                            </php>
                                            <br>
                                            <small class="text-muted">
                                                学历：{$qualifications[$post['qualification']] ?? '未设置'} | 
                                                性别：{$sexes[$post['sex']] ?? '未设置'}
                                                <php>if($post['service_price']){</php> | 价格：{$post.service_price}<php>}</php>
                                            </small>
                                        </label>
                                    </div>
                                <php>
                                }
                                if($current_project != '') {
                                    echo '</div></div></div>';
                                }
                                </php>
                            </div>
                            <php>}</php>
                        </div>
                    </div>

                    <!-- 已选择的岗位统计 -->
                    <div class="panel panel-success">
                        <div class="panel-heading">
                            <h3 class="panel-title">
                                <i class="fa fa-check-circle"></i> 已选择岗位统计
                            </h3>
                        </div>
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="stat-box text-center">
                                        <div class="stat-number text-primary" id="total-posts">{$all_posts|count}</div>
                                        <div class="stat-label">总岗位数</div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="stat-box text-center">
                                        <div class="stat-number text-success" id="selected-posts">{$linked_posts|count}</div>
                                        <div class="stat-label">已选择</div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="stat-box text-center">
                                        <div class="stat-number text-warning" id="unselected-posts">
                                            <php>echo count($all_posts) - count($linked_posts);</php>
                                        </div>
                                        <div class="stat-label">未选择</div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="stat-box text-center">
                                        <div class="stat-number text-info" id="selection-rate">
                                            <php>echo count($all_posts) > 0 ? round(count($linked_posts) / count($all_posts) * 100, 1) : 0;</php>%
                                        </div>
                                        <div class="stat-label">选择率</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="form-group">
                        <button type="submit" class="btn btn-primary">
                            <i class="fa fa-save"></i> 保存关联
                        </button>
                        <a href="{:U('recruitmentnotice/detail', array('id'=>$notice['id']))}" class="btn btn-default">
                            <i class="fa fa-arrow-left"></i> 返回详情
                        </a>
                        <button type="button" class="btn btn-info" onclick="previewSelection()">
                            <i class="fa fa-eye"></i> 预览选择
                        </button>
                    </div>
                </form>

                <!-- 操作提示 -->
                <div class="alert alert-info">
                    <h4><i class="fa fa-lightbulb-o"></i> 操作提示</h4>
                    <ul class="mb-0">
                        <li>选择要关联到此招聘公告的岗位</li>
                        <li>可以选择多个不同项目的岗位</li>
                        <li>保存后可以为每个岗位单独配置筛选要求</li>
                        <li>建议选择相关性较高的岗位以提高匹配效果</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.stat-box {
    padding: 15px;
    border: 1px solid #e7e7e7;
    border-radius: 4px;
    margin-bottom: 15px;
}
.stat-number {
    font-size: 28px;
    font-weight: bold;
    margin-bottom: 5px;
}
.stat-label {
    font-size: 12px;
    color: #666;
}
.checkbox {
    margin-bottom: 10px;
}
.panel-body .checkbox:last-child {
    margin-bottom: 0;
}
</style>

<script>
$(function(){
    // 监听复选框变化，更新统计
    $('input[name="post_ids[]"]').change(function(){
        updateStats();
    });
    
    // 表单提交处理
    $('.js-ajax-form').on('submit', function(e) {
        var selectedCount = $('input[name="post_ids[]"]:checked').length;
        
        if (selectedCount === 0) {
            layer.msg('请至少选择一个岗位', {icon: 2});
            return false;
        }
        
        // 显示保存状态
        var $submitBtn = $(this).find('button[type="submit"]');
        var originalText = $submitBtn.html();
        $submitBtn.html('<i class="fa fa-spinner fa-spin"></i> 保存中...');
        $submitBtn.prop('disabled', true);
        
        // 恢复按钮状态（如果提交失败）
        setTimeout(function() {
            $submitBtn.html(originalText);
            $submitBtn.prop('disabled', false);
        }, 3000);
    });
});

// 全选
function selectAll() {
    $('input[name="post_ids[]"]').prop('checked', true);
    updateStats();
}

// 全不选
function selectNone() {
    $('input[name="post_ids[]"]').prop('checked', false);
    updateStats();
}

// 更新统计信息
function updateStats() {
    var total = $('input[name="post_ids[]"]').length;
    var selected = $('input[name="post_ids[]"]:checked').length;
    var unselected = total - selected;
    var rate = total > 0 ? Math.round(selected / total * 100 * 10) / 10 : 0;
    
    $('#selected-posts').text(selected);
    $('#unselected-posts').text(unselected);
    $('#selection-rate').text(rate + '%');
}

// 预览选择
function previewSelection() {
    var selected = [];
    $('input[name="post_ids[]"]:checked').each(function(){
        var label = $(this).parent().find('strong').text();
        selected.push(label);
    });
    
    if (selected.length === 0) {
        layer.msg('请先选择岗位', {icon: 2});
        return;
    }
    
    var content = '已选择 ' + selected.length + ' 个岗位：<br><br>';
    content += selected.join('<br>');
    
    layer.open({
        type: 1,
        title: '预览选择的岗位',
        content: '<div style="padding: 20px;">' + content + '</div>',
        area: ['500px', '400px']
    });
}
</script>

<include file="block/footer" />
