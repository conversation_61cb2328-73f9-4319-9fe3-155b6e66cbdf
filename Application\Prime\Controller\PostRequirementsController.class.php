<?php
namespace Prime\Controller;

use Common\Controller\PrimeController;

/**
 * 岗位要求配置控制器
 */
class PostRequirementsController extends PrimeController
{
    public function _initialize()
    {
        parent::_initialize();
    }

    /**
     * 岗位要求配置页面
     */
    public function index()
    {
        $notice_id = intval(I('get.notice_id'));
        if (!$notice_id) {
            $this->error('参数错误');
        }
        
        // 获取招聘公告信息
        $notice = M('recruitment_notice')->where(['id' => $notice_id])->find();
        if (!$notice) {
            $this->error('招聘公告不存在');
        }

        // 获取公告关联的岗位
        $posts = M('recruitment_notice_post')
            ->alias('rnp')
            ->join('LEFT JOIN __PROJECT_POST__ pp ON rnp.post_id = pp.id')
            ->join('LEFT JOIN __PROJECT__ p ON pp.project_id = p.id')
            ->where(['rnp.notice_id' => $notice_id])
            ->field('pp.id as post_id, pp.job_name, p.name as project_name')
            ->select();

        // 获取已配置的要求
        $requirements = M('post_requirements')->where(['notice_id' => $notice_id])->select();
        $requirementsMap = [];
        foreach ($requirements as $req) {
            $requirementsMap[$req['post_id']] = $req;
        }
        
        $this->assign('notice', $notice);
        $this->assign('posts', $posts);
        $this->assign('requirements_map', $requirementsMap);
        
        $this->display();
    }

    /**
     * 添加测试数据
     */
    public function addTestData()
    {
        // 为招聘公告ID为1添加岗位关联
        $notice_id = 1;
        $post_ids = [1, 2, 3]; // 添加前3个岗位

        foreach ($post_ids as $post_id) {
            $data = [
                'notice_id' => $notice_id,
                'post_id' => $post_id,
                'create_time' => time()
            ];

            // 检查是否已存在
            $exists = M('recruitment_notice_post')->where($data)->find();
            if (!$exists) {
                M('recruitment_notice_post')->add($data);
                echo "Added post_id: $post_id to notice_id: $notice_id<br>";
            } else {
                echo "Post_id: $post_id already exists for notice_id: $notice_id<br>";
            }
        }

        // 添加一些岗位筛选要求的测试数据
        $requirements_data = [
            [
                'notice_id' => 1,
                'post_id' => 1,
                'min_age' => 18,
                'max_age' => 35,
                'gender' => 0, // 不限
                'education_level' => 2, // 大专及以上
                'create_time' => time(),
                'update_time' => time()
            ],
            [
                'notice_id' => 1,
                'post_id' => 2,
                'min_age' => 22,
                'max_age' => 40,
                'gender' => 1, // 男
                'education_level' => 3, // 本科及以上
                'create_time' => time(),
                'update_time' => time()
            ]
        ];

        foreach ($requirements_data as $req_data) {
            $exists = M('post_requirements')->where([
                'notice_id' => $req_data['notice_id'],
                'post_id' => $req_data['post_id']
            ])->find();

            if (!$exists) {
                M('post_requirements')->add($req_data);
                echo "Added requirements for post_id: {$req_data['post_id']}<br>";
            } else {
                echo "Requirements already exist for post_id: {$req_data['post_id']}<br>";
            }
        }

        echo "Test data added successfully!<br>";
        echo '<a href="' . U('PostRequirements/index', ['notice_id' => 1]) . '">返回岗位要求配置页面</a>';
    }

    /**
     * 编辑岗位要求
     */
    public function edit()
    {
        $notice_id = intval(I('get.notice_id'));
        $post_id = intval(I('get.post_id'));
        
        if (!$notice_id || !$post_id) {
            $this->error('参数错误');
        }
        
        // 获取招聘公告信息
        $notice = M('recruitment_notice')->where(['id' => $notice_id])->find();
        if (!$notice) {
            $this->error('招聘公告不存在');
        }

        // 获取岗位信息
        $post = M('project_post')
            ->alias('pp')
            ->join('LEFT JOIN __PROJECT__ p ON pp.project_id = p.id')
            ->where(['pp.id' => $post_id])
            ->field('pp.id, pp.job_name, p.name as project_name')
            ->find();
        if (!$post) {
            $this->error('岗位不存在');
        }

        // 获取已有的要求配置
        $requirements = M('post_requirements')->where([
            'notice_id' => $notice_id,
            'post_id' => $post_id
        ])->find();
        
        if (IS_POST) {
            $data = [
                'notice_id' => $notice_id,
                'post_id' => $post_id,
                'min_age' => intval(I('post.min_age', 0)) ?: null,
                'max_age' => intval(I('post.max_age', 0)) ?: null,
                'gender' => intval(I('post.gender', 0)),
                'min_height' => intval(I('post.min_height', 0)) ?: null,
                'max_height' => intval(I('post.max_height', 0)) ?: null,
                'is_afraid_heights' => intval(I('post.is_afraid_heights', 0)),
                'education_level' => intval(I('post.education_level', 0)),
                'min_work_years' => intval(I('post.min_work_years', 0)) ?: null,
                'max_work_years' => intval(I('post.max_work_years', 0)) ?: null,
                'major_keywords' => trim(I('post.major_keywords', '')),
                'health_status' => trim(I('post.health_status', '')),
                'political_status' => trim(I('post.political_status', '')),
                'marital_status' => intval(I('post.marital_status', 0)),
                'update_time' => time()
            ];
            
            $model = M('post_requirements');
            
            if ($requirements) {
                // 更新
                $result = $model->where([
                    'notice_id' => $notice_id,
                    'post_id' => $post_id
                ])->save($data);
            } else {
                // 新增
                $data['create_time'] = time();
                $result = $model->add($data);
            }
            
            if ($result !== false) {
                $this->success('保存成功', U('PostRequirements/index', ['notice_id' => $notice_id]));
            } else {
                $this->error('保存失败');
            }
        }
        
        $this->assign('notice', $notice);
        $this->assign('post', $post);
        $this->assign('requirements', $requirements);
        
        $this->display();
    }

    /**
     * 预览筛选结果
     */
    public function preview()
    {
        $notice_id = intval(I('get.notice_id'));
        $post_id = intval(I('get.post_id'));
        
        if (!$notice_id || !$post_id) {
            $this->error('参数错误');
        }
        
        // 获取招聘公告信息
        $notice = M('recruitment_notice')->where(['id' => $notice_id])->find();
        if (!$notice) {
            $this->error('招聘公告不存在');
        }

        // 获取岗位信息
        $post = M('project_post')
            ->alias('pp')
            ->join('LEFT JOIN __PROJECT__ p ON pp.project_id = p.id')
            ->where(['pp.id' => $post_id])
            ->field('pp.id, pp.job_name, p.name as project_name')
            ->find();
        if (!$post) {
            $this->error('岗位不存在');
        }

        // 获取要求配置
        $requirements = M('post_requirements')->where([
            'notice_id' => $notice_id,
            'post_id' => $post_id
        ])->find();
        
        if (!$requirements) {
            $this->error('该岗位还未配置要求');
        }
        
        // 构建筛选条件
        $where = [];
        
        // 年龄筛选
        if ($requirements['min_age']) {
            $where['age'] = ['egt', $requirements['min_age']];
        }
        if ($requirements['max_age']) {
            if (isset($where['age'])) {
                $where['age'] = ['between', [$requirements['min_age'], $requirements['max_age']]];
            } else {
                $where['age'] = ['elt', $requirements['max_age']];
            }
        }
        
        // 性别筛选
        if ($requirements['gender']) {
            $where['gender'] = $requirements['gender'];
        }
        
        // 身高筛选
        if ($requirements['min_height']) {
            $where['height'] = ['egt', $requirements['min_height']];
        }
        if ($requirements['max_height']) {
            if (isset($where['height'])) {
                $where['height'] = ['between', [$requirements['min_height'], $requirements['max_height']]];
            } else {
                $where['height'] = ['elt', $requirements['max_height']];
            }
        }
        
        // 学历筛选
        if ($requirements['education_level']) {
            $where['education_level'] = ['egt', $requirements['education_level']];
        }
        
        // 工作经验筛选
        if ($requirements['min_work_years']) {
            $where['work_years'] = ['egt', $requirements['min_work_years']];
        }
        if ($requirements['max_work_years']) {
            if (isset($where['work_years'])) {
                $where['work_years'] = ['between', [$requirements['min_work_years'], $requirements['max_work_years']]];
            } else {
                $where['work_years'] = ['elt', $requirements['max_work_years']];
            }
        }
        
        // 恐高筛选
        if ($requirements['is_afraid_heights'] == 1) {
            $where['is_afraid_heights'] = 0; // 不能恐高
        }
        
        // 婚姻状况筛选
        if ($requirements['marital_status']) {
            $where['marital_status'] = $requirements['marital_status'];
        }
        
        // 获取符合条件的简历
        $userModel = M('user');
        $count = $userModel->where($where)->count();
        
        // 分页
        $page = $this->page($count, 20);
        $list = $userModel->where($where)
            ->field('id, name, gender, age, height, education_level, work_years, is_afraid_heights, marital_status')
            ->limit($page->firstRow, $page->listRows)
            ->select();
        
        $this->assign('notice', $notice);
        $this->assign('post', $post);
        $this->assign('requirements', $requirements);
        $this->assign('list', $list);
        $this->assign('count', $count);
        $this->assign('page', $page->show());
        
        $this->display();
    }

    /**
     * 批量配置要求
     */
    public function batch()
    {
        $notice_id = intval(I('get.notice_id'));
        if (!$notice_id) {
            $this->error('参数错误');
        }
        
        // 获取招聘公告信息
        $notice = M('recruitment_notice')->where(['id' => $notice_id])->find();
        if (!$notice) {
            $this->error('招聘公告不存在');
        }

        // 获取公告关联的岗位
        $posts = M('recruitment_notice_post')
            ->alias('rnp')
            ->join('LEFT JOIN __PROJECT_POST__ pp ON rnp.post_id = pp.id')
            ->join('LEFT JOIN __PROJECT__ p ON pp.project_id = p.id')
            ->where(['rnp.notice_id' => $notice_id])
            ->field('pp.id as post_id, pp.job_name, p.name as project_name')
            ->select();
        
        if (IS_POST) {
            $post_ids = I('post.post_ids', []);
            $requirements = [
                'min_age' => intval(I('post.min_age', 0)) ?: null,
                'max_age' => intval(I('post.max_age', 0)) ?: null,
                'gender' => intval(I('post.gender', 0)),
                'min_height' => intval(I('post.min_height', 0)) ?: null,
                'max_height' => intval(I('post.max_height', 0)) ?: null,
                'is_afraid_heights' => intval(I('post.is_afraid_heights', 0)),
                'education_level' => intval(I('post.education_level', 0)),
                'min_work_years' => intval(I('post.min_work_years', 0)) ?: null,
                'max_work_years' => intval(I('post.max_work_years', 0)) ?: null,
                'major_keywords' => trim(I('post.major_keywords', '')),
                'health_status' => trim(I('post.health_status', '')),
                'political_status' => trim(I('post.political_status', '')),
                'marital_status' => intval(I('post.marital_status', 0))
            ];
            
            if (empty($post_ids)) {
                $this->error('请选择要配置的岗位');
            }
            
            $model = M('post_requirements');
            $success = 0;
            
            foreach ($post_ids as $post_id) {
                $data = $requirements;
                $data['notice_id'] = $notice_id;
                $data['post_id'] = intval($post_id);
                $data['update_time'] = time();
                
                // 检查是否已存在
                $exists = $model->where([
                    'notice_id' => $notice_id,
                    'post_id' => $post_id
                ])->find();
                
                if ($exists) {
                    // 更新
                    $result = $model->where([
                        'notice_id' => $notice_id,
                        'post_id' => $post_id
                    ])->save($data);
                } else {
                    // 新增
                    $data['create_time'] = time();
                    $result = $model->add($data);
                }
                
                if ($result !== false) {
                    $success++;
                }
            }
            
            $this->success("批量配置成功，共配置了 {$success} 个岗位", U('PostRequirements/index', ['notice_id' => $notice_id]));
        }
        
        $this->assign('notice', $notice);
        $this->assign('posts', $posts);
        
        $this->display();
    }
}
