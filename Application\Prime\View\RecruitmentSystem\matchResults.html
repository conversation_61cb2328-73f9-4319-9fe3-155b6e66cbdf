<include file="block/hat" />
<style>
/* 匹配结果页面样式 */
.match-results-header {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    padding: 20px;
    border-radius: 8px 8px 0 0;
    margin-bottom: 0;
}

.match-results-header h4 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.match-results-header .btn {
    background: rgba(255,255,255,0.2);
    border: 1px solid rgba(255,255,255,0.3);
    color: white;
    margin-left: 5px;
    transition: all 0.3s ease;
}

.match-results-header .btn:hover {
    background: rgba(255,255,255,0.3);
    border-color: rgba(255,255,255,0.5);
    color: white;
    transform: translateY(-1px);
}

.filter-panel {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-top: none;
    padding: 20px;
}

.match-table-container {
    background: white;
    border-radius: 0 0 8px 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.match-table {
    margin-bottom: 0;
}

.match-table thead th {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 2px solid #28a745;
    font-weight: 600;
    color: #495057;
    padding: 15px 12px;
}

.match-table tbody tr {
    transition: all 0.3s ease;
    border-bottom: 1px solid #f1f3f4;
}

.match-table tbody tr:hover {
    background: linear-gradient(90deg, rgba(40, 167, 69, 0.05) 0%, rgba(32, 201, 151, 0.02) 100%);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.match-table tbody td {
    padding: 15px 12px;
    vertical-align: middle;
    border-top: none;
}

.resume-info {
    display: flex;
    align-items: center;
}

.resume-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    margin-right: 12px;
    font-size: 16px;
}

.resume-details h5 {
    margin: 0 0 5px 0;
    font-weight: 600;
    color: #2c3e50;
}

.resume-meta {
    color: #6c757d;
    font-size: 12px;
    line-height: 1.4;
}

.job-info {
    font-weight: 600;
    color: #495057;
    margin-bottom: 5px;
}

.project-name {
    color: #6c757d;
    font-size: 12px;
}

.score-display {
    text-align: center;
    min-width: 120px;
}

.score-progress {
    height: 8px;
    border-radius: 10px;
    margin-bottom: 8px;
    overflow: hidden;
    background: #e9ecef;
}

.score-bar {
    height: 100%;
    border-radius: 10px;
    transition: width 0.8s ease;
}

.score-excellent { background: linear-gradient(90deg, #28a745, #20c997); }
.score-good { background: linear-gradient(90deg, #17a2b8, #6f42c1); }
.score-fair { background: linear-gradient(90deg, #ffc107, #fd7e14); }
.score-poor { background: linear-gradient(90deg, #dc3545, #e83e8c); }

.score-text {
    font-weight: 700;
    font-size: 16px;
}

.qualified-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

.qualified-yes {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
}

.qualified-no {
    background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
    color: white;
}

.action-buttons-match {
    display: flex;
    gap: 4px;
}

.action-buttons-match .btn {
    padding: 8px 12px;
    font-size: 12px;
    border-radius: 6px;
    transition: all 0.3s ease;
    border: none;
    font-weight: 500;
}

.action-buttons-match .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

.btn-detail { background: #17a2b8; color: white; }
.btn-resume { background: #007bff; color: white; }

.empty-match-state {
    text-align: center;
    padding: 80px 20px;
    color: #6c757d;
}

.empty-match-state i {
    font-size: 64px;
    margin-bottom: 20px;
    opacity: 0.3;
}
</style>

<div class="container-fluid">
    <div class="row">
        <include file="block/menu" />
        <div class="col-xs-12 col-sm-9 col-lg-10">
            <div class="main">
                <div class="panel panel-default" style="border: none; box-shadow: 0 4px 20px rgba(0,0,0,0.1); border-radius: 8px;">
                    <div class="match-results-header">
                        <div class="row">
                            <div class="col-md-6">
                                <h4>
                                    <i class="fa fa-eye"></i> 匹配结果
                                    <small style="opacity: 0.8; margin-left: 10px;">{$notice.title}</small>
                                </h4>
                            </div>
                            <div class="col-md-6 text-right">
                                <a href="{:U('exportResults', ['notice_id' => $notice['id']])}" class="btn btn-sm">
                                    <i class="fa fa-download"></i> 导出结果
                                </a>
                                <a href="{:U('executeMatch', ['notice_id' => $notice['id']])}" class="btn btn-sm" onclick="return confirm('确定要重新执行筛选吗？这将覆盖现有结果。')">
                                    <i class="fa fa-refresh"></i> 重新筛选
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- 筛选条件 -->
                    <div class="filter-panel">
                        <form class="form-inline" method="get" action="{:U('matchResults')}">
                            <input type="hidden" name="notice_id" value="{$notice.id}">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group" style="width: 100%;">
                                        <label style="display: block; margin-bottom: 5px; font-weight: 600; color: #495057;">
                                            <i class="fa fa-briefcase"></i> 筛选岗位
                                        </label>
                                        <select name="post_id" class="form-control" style="width: 100%;">
                                            <option value="">全部岗位</option>
                                            <volist name="postList" id="post">
                                                <option value="{$post.id}" <if condition="$post_id eq $post['id']">selected</if>>
                                                    {$post.project_name} - {$post.job_name}
                                                </option>
                                            </volist>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group" style="width: 100%;">
                                        <label style="display: block; margin-bottom: 5px; font-weight: 600; color: #495057;">
                                            <i class="fa fa-check-circle"></i> 匹配状态
                                        </label>
                                        <select name="qualified" class="form-control" style="width: 100%;">
                                            <option value="">全部状态</option>
                                            <option value="1" <if condition="$qualified eq '1'">selected</if>>✅ 符合条件</option>
                                            <option value="0" <if condition="$qualified eq '0'">selected</if>>❌ 不符合条件</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group" style="width: 100%;">
                                        <label style="display: block; margin-bottom: 5px; font-weight: 600; color: #495057;">
                                            <i class="fa fa-filter"></i> 操作
                                        </label>
                                        <div class="btn-group" style="width: 100%;">
                                            <button type="submit" class="btn btn-primary" style="border-radius: 4px 0 0 4px; width: 70%;">
                                                <i class="fa fa-search"></i> 筛选
                                            </button>
                                            <a href="{:U('matchResults', ['notice_id' => $notice['id']])}" class="btn btn-default" style="border-radius: 0 4px 4px 0; width: 30%;">
                                                <i class="fa fa-refresh"></i>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>

                    <div class="match-table-container">
                        <table class="table match-table">
                            <thead>
                                <tr>
                                    <th width="280">简历信息</th>
                                    <th width="180">岗位信息</th>
                                    <th width="140">匹配分数</th>
                                    <th width="120">匹配状态</th>
                                    <th width="120">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <volist name="list" id="v">
                                <empty name="list">
                                    <tr>
                                        <td colspan="5" class="empty-match-state">
                                            <i class="fa fa-search"></i>
                                            <h5>暂无匹配结果</h5>
                                            <p>请先执行简历筛选，或调整筛选条件</p>
                                        </td>
                                    </tr>
                                </empty>
                                    <tr>
                                        <td>
                                            <div class="resume-info">
                                                <div class="resume-avatar">
                                                    {$v.name|mb_substr=0,1,'utf-8'}
                                                </div>
                                                <div class="resume-details">
                                                    <h5>{$v.name} <small style="color: #6c757d;">({$v.gender})</small></h5>
                                                    <div class="resume-meta">
                                                        <i class="fa fa-phone"></i> {$v.phone}<br>
                                                        <i class="fa fa-graduation-cap"></i> {$v.education_level}
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="job-info">{$v.job_name}</div>
                                            <div class="project-name">
                                                <i class="fa fa-building-o"></i> {$v.project_name}
                                            </div>
                                        </td>
                                        <td>
                                            <div class="score-display">
                                                <div class="score-progress">
                                                    <div class="score-bar
                                                        <if condition="$v['match_score'] >= 90">score-excellent
                                                        <elseif condition="$v['match_score'] >= 80" />score-good
                                                        <elseif condition="$v['match_score'] >= 70" />score-fair
                                                        <else />score-poor
                                                        </if>"
                                                        style="width: {$v.match_score}%">
                                                    </div>
                                                </div>
                                                <div class="score-text
                                                    <if condition="$v['match_score'] >= 90">text-success
                                                    <elseif condition="$v['match_score'] >= 80" />text-info
                                                    <elseif condition="$v['match_score'] >= 70" />text-warning
                                                    <else />text-danger
                                                    </if>">
                                                    {$v.match_score}分
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <if condition="$v.is_qualified eq 1">
                                                <span class="qualified-badge qualified-yes">
                                                    <i class="fa fa-check"></i> 符合
                                                </span>
                                            <else />
                                                <span class="qualified-badge qualified-no">
                                                    <i class="fa fa-times"></i> 不符合
                                                </span>
                                            </if>
                                        </td>
                                        <td>
                                            <div class="action-buttons-match">
                                                <a href="{:U('matchDetail', ['id' => $v['id']])}" class="btn btn-detail" title="查看匹配详情">
                                                    <i class="fa fa-eye"></i>
                                                </a>
                                                <a href="{:U('Userjob/joballinfo', ['id' => $v['user_job_id']])}" class="btn btn-resume" title="查看完整简历" target="_blank">
                                                    <i class="fa fa-file-text"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                </volist>
                            </tbody>
                        </table>
                    </div>

                    <if condition="$page">
                        <div class="pagination-wrapper">
                            <div class="row">
                                <div class="col-md-6">
                                    <div style="line-height: 34px; color: #6c757d;">
                                        <i class="fa fa-info-circle"></i>
                                        显示简历匹配结果，按匹配分数降序排列
                                    </div>
                                </div>
                                <div class="col-md-6 text-right">
                                    {$page}
                                </div>
                            </div>
                        </div>
                    </if>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // 表格行动画效果
    $('.match-table tbody tr').each(function(index) {
        $(this).css('animation-delay', (index * 0.1) + 's');
        $(this).addClass('fadeInUp');
    });

    // 分数进度条动画
    $('.score-bar').each(function() {
        var $this = $(this);
        var width = $this.css('width');
        $this.css('width', '0%');
        setTimeout(function() {
            $this.animate({width: width}, 1000, 'easeOutCubic');
        }, 500 + Math.random() * 500);
    });

    // 筛选表单增强
    $('.filter-panel select').on('change', function() {
        $(this).closest('.form-group').addClass('changed');
        setTimeout(function() {
            $('.form-group.changed').removeClass('changed');
        }, 300);
    });

    // 悬停效果增强
    $('.match-table tbody tr').hover(
        function() {
            $(this).find('.resume-avatar').addClass('avatar-hover');
            $(this).find('.action-buttons-match .btn').addClass('btn-hover-scale');
        },
        function() {
            $(this).find('.resume-avatar').removeClass('avatar-hover');
            $(this).find('.action-buttons-match .btn').removeClass('btn-hover-scale');
        }
    );

    // 重新筛选确认
    $('a[href*="executeMatch"]').click(function(e) {
        e.preventDefault();
        var $this = $(this);

        if (confirm('🔄 重新执行筛选\n\n这将重新分析所有简历并覆盖现有匹配结果。\n根据简历数量，可能需要几分钟时间。\n\n确定要继续吗？')) {
            // 显示加载状态
            $this.html('<i class="fa fa-spinner fa-spin"></i> 执行中...');
            $this.addClass('disabled');
            window.location.href = $this.attr('href');
        }
    });
});

// 添加CSS动画
$('<style>').text(`
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fadeInUp {
    animation: fadeInUp 0.6s ease forwards;
}

.avatar-hover {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.btn-hover-scale {
    transform: scale(1.1);
}

.form-group.changed {
    transform: scale(1.02);
    transition: transform 0.3s ease;
}

/* 自定义缓动函数 */
jQuery.easing.easeOutCubic = function (x, t, b, c, d) {
    return c*((t=t/d-1)*t*t + 1) + b;
};
`).appendTo('head');
</script>

<include file="block/footer" />
