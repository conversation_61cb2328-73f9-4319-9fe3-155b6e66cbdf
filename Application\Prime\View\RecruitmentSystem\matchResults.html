<include file="block/hat" />
<div class="container-fluid">
    <div class="row">
        <include file="block/menu" />
        <div class="col-xs-12 col-sm-9 col-lg-10">
            <ul class="nav nav-tabs">
                <li class="active"><a>匹配结果</a></li>
            </ul>
            <style type='text/css'>.tab-pane {padding:20px 0 20px 0;}</style>
            <div class="main">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <div class="row">
                            <div class="col-md-6">
                                <h4 class="panel-title">
                                    <i class="fa fa-eye"></i> 匹配结果 - {$notice.title}
                                </h4>
                            </div>
                            <div class="col-md-6 text-right">
                                <a href="{:U('exportResults', ['notice_id' => $notice['id']])}" class="btn btn-success btn-sm">
                                    <i class="fa fa-download"></i> 导出结果
                                </a>
                                <a href="{:U('executeMatch', ['notice_id' => $notice['id']])}" class="btn btn-warning btn-sm" onclick="return confirm('确定要重新执行筛选吗？')">
                                    <i class="fa fa-refresh"></i> 重新筛选
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 筛选条件 -->
                    <div class="panel-body">
                        <form class="form-inline" method="get" action="{:U('matchResults')}">
                            <input type="hidden" name="notice_id" value="{$notice.id}">
                            <div class="form-group">
                                <label>岗位：</label>
                                <select name="post_id" class="form-control">
                                    <option value="">全部岗位</option>
                                    <volist name="postList" id="post">
                                        <option value="{$post.id}" <if condition="$post_id eq $post['id']">selected</if>>
                                            {$post.project_name} - {$post.job_name}
                                        </option>
                                    </volist>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>匹配状态：</label>
                                <select name="qualified" class="form-control">
                                    <option value="">全部</option>
                                    <option value="1" <if condition="$qualified eq '1'">selected</if>>符合条件</option>
                                    <option value="0" <if condition="$qualified eq '0'">selected</if>>不符合条件</option>
                                </select>
                            </div>
                            <button type="submit" class="btn btn-default">
                                <i class="fa fa-filter"></i> 筛选
                            </button>
                            <a href="{:U('matchResults', ['notice_id' => $notice['id']])}" class="btn btn-default">
                                <i class="fa fa-refresh"></i> 重置
                            </a>
                        </form>
                    </div>

                    <div class="panel-body table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>简历信息</th>
                                    <th>岗位</th>
                                    <th>匹配分数</th>
                                    <th>匹配状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <volist name="list" id="v" empty="<tr><td colspan='5' class='text-center text-muted'>暂无匹配结果</td></tr>">
                                    <tr>
                                        <td>
                                            <div class="media">
                                                <div class="media-body">
                                                    <h5 class="media-heading">
                                                        <strong>{$v.name}</strong>
                                                        <small class="text-muted">({$v.gender})</small>
                                                    </h5>
                                                    <div class="text-muted">
                                                        <small>
                                                            <i class="fa fa-phone"></i> {$v.phone}<br>
                                                            <i class="fa fa-graduation-cap"></i> {$v.education_level}
                                                        </small>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <strong>{$v.job_name}</strong><br>
                                            <small class="text-muted">{$v.project_name}</small>
                                        </td>
                                        <td>
                                            <div class="progress" style="margin-bottom: 5px;">
                                                <div class="progress-bar 
                                                    <if condition="$v['match_score'] >= 90">progress-bar-success
                                                    <elseif condition="$v['match_score'] >= 80" />progress-bar-info
                                                    <elseif condition="$v['match_score'] >= 70" />progress-bar-warning
                                                    <else />progress-bar-danger
                                                    </if>" 
                                                    style="width: {$v.match_score}%">
                                                </div>
                                            </div>
                                            <strong>{$v.match_score}分</strong>
                                        </td>
                                        <td>
                                            <if condition="$v.is_qualified eq 1">
                                                <span class="label label-success">符合条件</span>
                                            <else />
                                                <span class="label label-danger">不符合条件</span>
                                            </if>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-xs">
                                                <a href="{:U('matchDetail', ['id' => $v['id']])}" class="btn btn-info" title="查看详情">
                                                    <i class="fa fa-eye"></i>
                                                </a>
                                                <a href="{:U('Userjob/joballinfo', ['id' => $v['user_job_id']])}" class="btn btn-primary" title="查看简历" target="_blank">
                                                    <i class="fa fa-file-text"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                </volist>
                            </tbody>
                        </table>
                        
                        <if condition="$page">
                            <div class="text-center">
                                {$page}
                            </div>
                        </if>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<include file="block/footer" />
