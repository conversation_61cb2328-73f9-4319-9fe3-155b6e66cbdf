-- 招聘公告智能筛选系统数据库表结构

-- 招聘公告表
CREATE TABLE `z_recruitment_notice` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL COMMENT '公告标题',
  `description` text COMMENT '公告描述',
  `company_name` varchar(255) DEFAULT NULL COMMENT '招聘单位',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：0-停用，1-启用',
  `create_time` int(11) DEFAULT NULL COMMENT '创建时间',
  `update_time` int(11) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='招聘公告表';

-- 招聘公告岗位关联表
CREATE TABLE `z_recruitment_notice_post` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `notice_id` int(11) NOT NULL COMMENT '招聘公告ID',
  `post_id` int(11) NOT NULL COMMENT '岗位ID',
  `create_time` int(11) DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_notice_id` (`notice_id`),
  KEY `idx_post_id` (`post_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='招聘公告岗位关联表';

-- 岗位筛选要求表
CREATE TABLE `z_post_requirements` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `post_id` int(11) NOT NULL COMMENT '岗位ID',
  `notice_id` int(11) NOT NULL COMMENT '招聘公告ID',
  
  -- 基本条件
  `min_age` tinyint(3) DEFAULT NULL COMMENT '最小年龄',
  `max_age` tinyint(3) DEFAULT NULL COMMENT '最大年龄',
  `gender` tinyint(1) DEFAULT 0 COMMENT '性别要求：0-不限，1-男，2-女',
  `min_height` smallint(5) DEFAULT NULL COMMENT '最小身高(cm)',
  `max_height` smallint(5) DEFAULT NULL COMMENT '最大身高(cm)',
  
  -- 学历要求
  `education_level` tinyint(1) DEFAULT 0 COMMENT '学历要求：0-不限，1-中专及以上，2-大专及以上，3-本科及以上，4-研究生及以上，5-硕士',
  `major_keywords` text COMMENT '专业关键词（多个用逗号分隔）',
  
  -- 工作经验
  `min_work_years` tinyint(3) DEFAULT NULL COMMENT '最少工作年限',
  `max_work_years` tinyint(3) DEFAULT NULL COMMENT '最多工作年限',
  
  -- 其他要求
  `health_status` varchar(50) DEFAULT NULL COMMENT '健康状况要求',
  `is_afraid_heights` tinyint(1) DEFAULT NULL COMMENT '恐高要求：0-不限，1-不能恐高，2-可以恐高',
  `political_status` varchar(100) DEFAULT NULL COMMENT '政治面貌要求',
  `marital_status` tinyint(1) DEFAULT NULL COMMENT '婚姻状况：0-不限，1-未婚，2-已婚',
  
  `create_time` int(11) DEFAULT NULL COMMENT '创建时间',
  `update_time` int(11) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_post_id` (`post_id`),
  KEY `idx_notice_id` (`notice_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='岗位筛选要求表';

-- 简历岗位匹配表
CREATE TABLE `z_resume_post_match` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_job_id` int(11) NOT NULL COMMENT '简历ID',
  `post_id` int(11) NOT NULL COMMENT '岗位ID',
  `notice_id` int(11) NOT NULL COMMENT '招聘公告ID',
  `match_score` decimal(5,2) DEFAULT 0.00 COMMENT '匹配分数(0-100)',
  `match_details` text COMMENT '匹配详情JSON',
  `is_qualified` tinyint(1) DEFAULT 0 COMMENT '是否符合要求：0-不符合，1-符合',
  `create_time` int(11) DEFAULT NULL COMMENT '创建时间',
  `update_time` int(11) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_job_post_notice` (`user_job_id`,`post_id`,`notice_id`),
  KEY `idx_post_id` (`post_id`),
  KEY `idx_notice_id` (`notice_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='简历岗位匹配表';
