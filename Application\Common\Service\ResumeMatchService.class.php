<?php
namespace Common\Service;

/**
 * 简历匹配服务类
 */
class ResumeMatchService
{
    /**
     * 匹配权重配置
     */
    private $weights = [
        'age' => 15,           // 年龄权重
        'gender' => 10,        // 性别权重
        'height' => 10,        // 身高权重
        'education' => 25,     // 学历权重
        'major' => 20,         // 专业权重
        'work_years' => 15,    // 工作经验权重
        'other' => 5           // 其他条件权重
    ];

    /**
     * 学历等级映射
     */
    private $educationLevels = [
        '小学' => 1,
        '初中' => 2,
        '中专' => 3,
        '高中' => 3,
        '大专' => 4,
        '本科' => 5,
        '研究生' => 6,
        '硕士' => 6,
        '博士' => 7
    ];

    /**
     * 执行简历匹配
     */
    public function matchResume($resume, $requirements)
    {
        $matchDetails = [];
        $totalScore = 0;
        $qualified = true;

        // 年龄匹配
        $ageResult = $this->matchAge($resume, $requirements);
        $matchDetails['age'] = $ageResult;
        $totalScore += $ageResult['score'] * $this->weights['age'] / 100;
        if (!$ageResult['qualified']) $qualified = false;

        // 性别匹配
        $genderResult = $this->matchGender($resume, $requirements);
        $matchDetails['gender'] = $genderResult;
        $totalScore += $genderResult['score'] * $this->weights['gender'] / 100;
        if (!$genderResult['qualified']) $qualified = false;

        // 身高匹配
        $heightResult = $this->matchHeight($resume, $requirements);
        $matchDetails['height'] = $heightResult;
        $totalScore += $heightResult['score'] * $this->weights['height'] / 100;
        if (!$heightResult['qualified']) $qualified = false;

        // 学历匹配
        $educationResult = $this->matchEducation($resume, $requirements);
        $matchDetails['education'] = $educationResult;
        $totalScore += $educationResult['score'] * $this->weights['education'] / 100;
        if (!$educationResult['qualified']) $qualified = false;

        // 专业匹配
        $majorResult = $this->matchMajor($resume, $requirements);
        $matchDetails['major'] = $majorResult;
        $totalScore += $majorResult['score'] * $this->weights['major'] / 100;

        // 工作经验匹配
        $workYearsResult = $this->matchWorkYears($resume, $requirements);
        $matchDetails['work_years'] = $workYearsResult;
        $totalScore += $workYearsResult['score'] * $this->weights['work_years'] / 100;

        // 其他条件匹配
        $otherResult = $this->matchOtherConditions($resume, $requirements);
        $matchDetails['other'] = $otherResult;
        $totalScore += $otherResult['score'] * $this->weights['other'] / 100;
        if (!$otherResult['qualified']) $qualified = false;

        return [
            'score' => round($totalScore, 2),
            'qualified' => $qualified,
            'details' => $matchDetails
        ];
    }

    /**
     * 年龄匹配
     */
    private function matchAge($resume, $requirements)
    {
        $age = $this->calculateAge($resume['id_number']);
        $minAge = $requirements['min_age'];
        $maxAge = $requirements['max_age'];

        if (empty($minAge) && empty($maxAge)) {
            return ['score' => 100, 'qualified' => true, 'message' => '年龄不限'];
        }

        $qualified = true;
        $message = "年龄{$age}岁";

        if (!empty($minAge) && $age < $minAge) {
            $qualified = false;
            $message .= "，低于最小年龄{$minAge}岁";
        }

        if (!empty($maxAge) && $age > $maxAge) {
            $qualified = false;
            $message .= "，超过最大年龄{$maxAge}岁";
        }

        if ($qualified) {
            $message .= '，符合年龄要求';
        }

        $score = $qualified ? 100 : 0;

        return ['score' => $score, 'qualified' => $qualified, 'message' => $message];
    }

    /**
     * 性别匹配
     */
    private function matchGender($resume, $requirements)
    {
        $gender = $requirements['gender'];
        
        if (empty($gender) || $gender == 0) {
            return ['score' => 100, 'qualified' => true, 'message' => '性别不限'];
        }

        $resumeGender = $resume['gender'] == '男' ? 1 : 2;
        $qualified = ($resumeGender == $gender);
        $genderText = $gender == 1 ? '男' : '女';
        
        $message = $qualified ? "性别{$resume['gender']}，符合要求" : "性别{$resume['gender']}，要求{$genderText}";
        $score = $qualified ? 100 : 0;

        return ['score' => $score, 'qualified' => $qualified, 'message' => $message];
    }

    /**
     * 身高匹配
     */
    private function matchHeight($resume, $requirements)
    {
        $height = intval($resume['height']);
        $minHeight = $requirements['min_height'];
        $maxHeight = $requirements['max_height'];

        if (empty($minHeight) && empty($maxHeight)) {
            return ['score' => 100, 'qualified' => true, 'message' => '身高不限'];
        }

        $qualified = true;
        $message = "身高{$height}cm";

        if (!empty($minHeight) && $height < $minHeight) {
            $qualified = false;
            $message .= "，低于最小身高{$minHeight}cm";
        }

        if (!empty($maxHeight) && $height > $maxHeight) {
            $qualified = false;
            $message .= "，超过最大身高{$maxHeight}cm";
        }

        if ($qualified) {
            $message .= '，符合身高要求';
        }

        $score = $qualified ? 100 : 0;

        return ['score' => $score, 'qualified' => $qualified, 'message' => $message];
    }

    /**
     * 学历匹配
     */
    private function matchEducation($resume, $requirements)
    {
        $requiredLevel = $requirements['education_level'];
        
        if (empty($requiredLevel) || $requiredLevel == 0) {
            return ['score' => 100, 'qualified' => true, 'message' => '学历不限'];
        }

        $resumeEducation = $resume['education_level'];
        $resumeLevel = $this->getEducationLevel($resumeEducation);
        
        // 学历要求映射：1-中专及以上，2-大专及以上，3-本科及以上，4-研究生及以上，5-硕士及以上
        $requiredMinLevel = $requiredLevel + 2; // 转换为内部等级
        
        $qualified = ($resumeLevel >= $requiredMinLevel);
        $message = $qualified ? "学历{$resumeEducation}，符合要求" : "学历{$resumeEducation}，不符合要求";
        
        // 学历超出要求可以加分
        $score = $qualified ? 100 : 0;
        if ($qualified && $resumeLevel > $requiredMinLevel) {
            $score = min(120, 100 + ($resumeLevel - $requiredMinLevel) * 5);
        }

        return ['score' => $score, 'qualified' => $qualified, 'message' => $message];
    }

    /**
     * 专业匹配
     */
    private function matchMajor($resume, $requirements)
    {
        $keywords = $requirements['major_keywords'];
        
        if (empty($keywords)) {
            return ['score' => 100, 'qualified' => true, 'message' => '专业不限'];
        }

        $resumeMajor = $resume['major'];
        $keywordArray = explode(',', $keywords);
        
        $matched = false;
        foreach ($keywordArray as $keyword) {
            $keyword = trim($keyword);
            if (!empty($keyword) && strpos($resumeMajor, $keyword) !== false) {
                $matched = true;
                break;
            }
        }

        $message = $matched ? "专业{$resumeMajor}，匹配关键词" : "专业{$resumeMajor}，未匹配关键词";
        $score = $matched ? 100 : 60; // 专业不匹配不算硬性不符合，但分数较低

        return ['score' => $score, 'qualified' => true, 'message' => $message];
    }

    /**
     * 工作经验匹配
     */
    private function matchWorkYears($resume, $requirements)
    {
        $workYears = intval($resume['work_experience_years']);
        $minYears = $requirements['min_work_years'];
        $maxYears = $requirements['max_work_years'];

        if (empty($minYears) && empty($maxYears)) {
            return ['score' => 100, 'qualified' => true, 'message' => '工作经验不限'];
        }

        $qualified = true;
        $message = "工作经验{$workYears}年";

        if (!empty($minYears) && $workYears < $minYears) {
            $qualified = false;
            $message .= "，低于最少{$minYears}年";
        }

        if (!empty($maxYears) && $workYears > $maxYears) {
            $message .= "，超过最多{$maxYears}年";
            // 工作经验超出不算不符合，但可能影响分数
        }

        if ($qualified) {
            $message .= '，符合经验要求';
        }

        $score = $qualified ? 100 : 70; // 经验不足扣分但不算完全不符合

        return ['score' => $score, 'qualified' => $qualified, 'message' => $message];
    }

    /**
     * 其他条件匹配
     */
    private function matchOtherConditions($resume, $requirements)
    {
        $score = 100;
        $qualified = true;
        $messages = [];

        // 健康状况
        if (!empty($requirements['health_status'])) {
            $healthMatch = ($resume['health_status'] == $requirements['health_status']);
            if (!$healthMatch) {
                $qualified = false;
                $messages[] = "健康状况不符合要求";
            } else {
                $messages[] = "健康状况符合要求";
            }
        }

        // 恐高要求
        if (!empty($requirements['is_afraid_heights']) && $requirements['is_afraid_heights'] != 0) {
            $afraidHeights = $resume['is_afraid_heights'];
            if ($requirements['is_afraid_heights'] == 1 && $afraidHeights == '是') {
                $qualified = false;
                $messages[] = "恐高，不符合要求";
            } else {
                $messages[] = "恐高要求符合";
            }
        }

        // 政治面貌
        if (!empty($requirements['political_status'])) {
            $politicalMatch = ($resume['political_status'] == $requirements['political_status']);
            if (!$politicalMatch) {
                $score -= 10; // 政治面貌不匹配扣分但不算硬性不符合
                $messages[] = "政治面貌不完全匹配";
            } else {
                $messages[] = "政治面貌符合要求";
            }
        }

        // 婚姻状况
        if (!empty($requirements['marital_status']) && $requirements['marital_status'] != 0) {
            $maritalStatus = $resume['marital_status'] == '已婚' ? 2 : 1;
            $maritalMatch = ($maritalStatus == $requirements['marital_status']);
            if (!$maritalMatch) {
                $score -= 5; // 婚姻状况不匹配轻微扣分
                $messages[] = "婚姻状况不完全匹配";
            } else {
                $messages[] = "婚姻状况符合要求";
            }
        }

        $message = empty($messages) ? '其他条件不限' : implode('；', $messages);

        return ['score' => max(0, $score), 'qualified' => $qualified, 'message' => $message];
    }

    /**
     * 根据身份证号计算年龄
     */
    private function calculateAge($idNumber)
    {
        if (strlen($idNumber) != 18) {
            return 0;
        }

        $birthYear = substr($idNumber, 6, 4);
        $birthMonth = substr($idNumber, 10, 2);
        $birthDay = substr($idNumber, 12, 2);
        
        $birthDate = $birthYear . '-' . $birthMonth . '-' . $birthDay;
        $birthTimestamp = strtotime($birthDate);
        
        if ($birthTimestamp === false) {
            return 0;
        }
        
        $age = floor((time() - $birthTimestamp) / (365 * 24 * 3600));
        return $age;
    }

    /**
     * 获取学历等级
     */
    private function getEducationLevel($education)
    {
        foreach ($this->educationLevels as $level => $value) {
            if (strpos($education, $level) !== false) {
                return $value;
            }
        }
        return 1; // 默认最低等级
    }
}
