<?php
namespace Common\Service;

/**
 * 简历匹配服务
 */
class ResumeMatchService
{
    /**
     * 匹配权重配置
     */
    private $weights = [
        'age' => 15,           // 年龄权重
        'gender' => 10,        // 性别权重
        'height' => 10,        // 身高权重
        'education' => 20,     // 学历权重
        'major' => 15,         // 专业权重
        'work_years' => 15,    // 工作经验权重
        'health' => 5,         // 健康状况权重
        'afraid_heights' => 5, // 恐高权重
        'political' => 3,      // 政治面貌权重
        'marital' => 2         // 婚姻状况权重
    ];

    /**
     * 学历等级映射
     */
    private $educationLevels = [
        '小学' => 1,
        '初中' => 2,
        '中专' => 3,
        '高中' => 3,
        '大专' => 4,
        '本科' => 5,
        '研究生' => 6,
        '硕士' => 6,
        '博士' => 7
    ];

    /**
     * 执行简历匹配
     * @param array $requirement 岗位要求
     * @return array 匹配结果
     */
    public function matchResumes($requirement)
    {
        // 获取所有简历
        $resumes = D('UserJob')->where(['job_state' => 0])->select(); // 只匹配待就业的简历
        
        $matches = [];
        
        foreach ($resumes as $resume) {
            $matchResult = $this->calculateMatch($resume, $requirement);
            
            if ($matchResult) {
                $matches[] = [
                    'user_job_id' => $resume['id'],
                    'post_id' => $requirement['post_id'],
                    'notice_id' => $requirement['notice_id'],
                    'match_score' => $matchResult['score'],
                    'match_details' => json_encode($matchResult['details']),
                    'is_qualified' => $matchResult['qualified'] ? 1 : 0
                ];
            }
        }
        
        return $matches;
    }

    /**
     * 计算单个简历的匹配度
     * @param array $resume 简历信息
     * @param array $requirement 岗位要求
     * @return array|false 匹配结果
     */
    private function calculateMatch($resume, $requirement)
    {
        $score = 0;
        $totalWeight = 0;
        $details = [];
        $qualified = true;
        
        // 年龄匹配
        $ageResult = $this->matchAge($resume, $requirement);
        if ($ageResult !== null) {
            $score += $ageResult['score'] * $this->weights['age'];
            $totalWeight += $this->weights['age'];
            $details['age'] = $ageResult;
            if (!$ageResult['match']) {
                $qualified = false;
            }
        }
        
        // 性别匹配
        $genderResult = $this->matchGender($resume, $requirement);
        if ($genderResult !== null) {
            $score += $genderResult['score'] * $this->weights['gender'];
            $totalWeight += $this->weights['gender'];
            $details['gender'] = $genderResult;
            if (!$genderResult['match']) {
                $qualified = false;
            }
        }
        
        // 身高匹配
        $heightResult = $this->matchHeight($resume, $requirement);
        if ($heightResult !== null) {
            $score += $heightResult['score'] * $this->weights['height'];
            $totalWeight += $this->weights['height'];
            $details['height'] = $heightResult;
            if (!$heightResult['match']) {
                $qualified = false;
            }
        }
        
        // 学历匹配
        $educationResult = $this->matchEducation($resume, $requirement);
        if ($educationResult !== null) {
            $score += $educationResult['score'] * $this->weights['education'];
            $totalWeight += $this->weights['education'];
            $details['education'] = $educationResult;
            if (!$educationResult['match']) {
                $qualified = false;
            }
        }
        
        // 专业匹配
        $majorResult = $this->matchMajor($resume, $requirement);
        if ($majorResult !== null) {
            $score += $majorResult['score'] * $this->weights['major'];
            $totalWeight += $this->weights['major'];
            $details['major'] = $majorResult;
        }
        
        // 工作经验匹配
        $workYearsResult = $this->matchWorkYears($resume, $requirement);
        if ($workYearsResult !== null) {
            $score += $workYearsResult['score'] * $this->weights['work_years'];
            $totalWeight += $this->weights['work_years'];
            $details['work_years'] = $workYearsResult;
            if (!$workYearsResult['match']) {
                $qualified = false;
            }
        }
        
        // 健康状况匹配
        $healthResult = $this->matchHealth($resume, $requirement);
        if ($healthResult !== null) {
            $score += $healthResult['score'] * $this->weights['health'];
            $totalWeight += $this->weights['health'];
            $details['health'] = $healthResult;
            if (!$healthResult['match']) {
                $qualified = false;
            }
        }
        
        // 恐高匹配
        $afraidHeightsResult = $this->matchAfraidHeights($resume, $requirement);
        if ($afraidHeightsResult !== null) {
            $score += $afraidHeightsResult['score'] * $this->weights['afraid_heights'];
            $totalWeight += $this->weights['afraid_heights'];
            $details['afraid_heights'] = $afraidHeightsResult;
            if (!$afraidHeightsResult['match']) {
                $qualified = false;
            }
        }
        
        // 政治面貌匹配
        $politicalResult = $this->matchPolitical($resume, $requirement);
        if ($politicalResult !== null) {
            $score += $politicalResult['score'] * $this->weights['political'];
            $totalWeight += $this->weights['political'];
            $details['political'] = $politicalResult;
        }
        
        // 婚姻状况匹配
        $maritalResult = $this->matchMarital($resume, $requirement);
        if ($maritalResult !== null) {
            $score += $maritalResult['score'] * $this->weights['marital'];
            $totalWeight += $this->weights['marital'];
            $details['marital'] = $maritalResult;
        }
        
        // 计算最终分数
        $finalScore = $totalWeight > 0 ? round($score / $totalWeight * 100, 2) : 0;
        
        return [
            'score' => $finalScore,
            'details' => $details,
            'qualified' => $qualified
        ];
    }

    /**
     * 年龄匹配
     */
    private function matchAge($resume, $requirement)
    {
        if (!$requirement['min_age'] && !$requirement['max_age']) {
            return null; // 不限年龄
        }
        
        if (!$resume['birthdate']) {
            return ['match' => false, 'score' => 0, 'reason' => '简历未填写出生日期'];
        }
        
        $age = date('Y') - date('Y', strtotime($resume['birthdate']));
        $match = true;
        $score = 100;
        $reason = "年龄{$age}岁";
        
        if ($requirement['min_age'] && $age < $requirement['min_age']) {
            $match = false;
            $score = 0;
            $reason .= "，低于最小年龄{$requirement['min_age']}岁";
        } elseif ($requirement['max_age'] && $age > $requirement['max_age']) {
            $match = false;
            $score = 0;
            $reason .= "，超过最大年龄{$requirement['max_age']}岁";
        } else {
            $reason .= '，符合年龄要求';
        }
        
        return ['match' => $match, 'score' => $score, 'reason' => $reason];
    }

    /**
     * 性别匹配
     */
    private function matchGender($resume, $requirement)
    {
        if (!$requirement['gender']) {
            return null; // 不限性别
        }
        
        $requiredGender = $requirement['gender'] == 1 ? '男' : '女';
        $match = $resume['gender'] == $requiredGender;
        $score = $match ? 100 : 0;
        $reason = "简历性别{$resume['gender']}，要求{$requiredGender}";
        
        return ['match' => $match, 'score' => $score, 'reason' => $reason];
    }

    /**
     * 身高匹配
     */
    private function matchHeight($resume, $requirement)
    {
        if (!$requirement['min_height'] && !$requirement['max_height']) {
            return null; // 不限身高
        }
        
        if (!$resume['height']) {
            return ['match' => false, 'score' => 0, 'reason' => '简历未填写身高'];
        }
        
        $height = $resume['height'];
        $match = true;
        $score = 100;
        $reason = "身高{$height}cm";
        
        if ($requirement['min_height'] && $height < $requirement['min_height']) {
            $match = false;
            $score = 0;
            $reason .= "，低于最小身高{$requirement['min_height']}cm";
        } elseif ($requirement['max_height'] && $height > $requirement['max_height']) {
            $match = false;
            $score = 0;
            $reason .= "，超过最大身高{$requirement['max_height']}cm";
        } else {
            $reason .= '，符合身高要求';
        }
        
        return ['match' => $match, 'score' => $score, 'reason' => $reason];
    }

    /**
     * 学历匹配
     */
    private function matchEducation($resume, $requirement)
    {
        if (!$requirement['education_level']) {
            return null; // 不限学历
        }
        
        if (!$resume['education_level']) {
            return ['match' => false, 'score' => 0, 'reason' => '简历未填写学历'];
        }
        
        $resumeLevel = $this->getEducationLevel($resume['education_level']);
        $requiredLevel = $requirement['education_level'];
        
        $match = $resumeLevel >= $requiredLevel;
        $score = $match ? (100 + ($resumeLevel - $requiredLevel) * 10) : 0; // 超出要求可以加分
        $score = min($score, 120); // 最高120分
        
        $requiredLevelText = $this->getEducationLevelText($requiredLevel);
        $reason = "简历学历{$resume['education_level']}，要求{$requiredLevelText}";
        
        return ['match' => $match, 'score' => $score, 'reason' => $reason];
    }

    /**
     * 获取学历等级
     */
    private function getEducationLevel($education)
    {
        foreach ($this->educationLevels as $level => $value) {
            if (strpos($education, $level) !== false) {
                return $value;
            }
        }
        return 0;
    }

    /**
     * 获取学历等级文本
     */
    private function getEducationLevelText($level)
    {
        $texts = [
            0 => '不限学历',
            1 => '中专及以上',
            2 => '大专及以上',
            3 => '本科及以上',
            4 => '研究生及以上',
            5 => '硕士'
        ];
        
        return $texts[$level] ?? '未知';
    }

    /**
     * 专业匹配
     */
    private function matchMajor($resume, $requirement)
    {
        if (!$requirement['major_keywords']) {
            return null; // 不限专业
        }
        
        if (!$resume['major']) {
            return ['match' => false, 'score' => 50, 'reason' => '简历未填写专业'];
        }
        
        $keywords = explode(',', $requirement['major_keywords']);
        $match = false;
        
        foreach ($keywords as $keyword) {
            $keyword = trim($keyword);
            if ($keyword && strpos($resume['major'], $keyword) !== false) {
                $match = true;
                break;
            }
        }
        
        $score = $match ? 100 : 30; // 专业不匹配也给一定分数
        $reason = "简历专业{$resume['major']}，要求包含关键词：" . $requirement['major_keywords'];
        
        return ['match' => $match, 'score' => $score, 'reason' => $reason];
    }

    /**
     * 工作经验匹配
     */
    private function matchWorkYears($resume, $requirement)
    {
        if (!$requirement['min_work_years'] && !$requirement['max_work_years']) {
            return null; // 不限工作经验
        }
        
        $workYears = $resume['work_experience_years'] ?: 0;
        $match = true;
        $score = 100;
        $reason = "工作经验{$workYears}年";
        
        if ($requirement['min_work_years'] && $workYears < $requirement['min_work_years']) {
            $match = false;
            $score = 0;
            $reason .= "，低于最少工作年限{$requirement['min_work_years']}年";
        } elseif ($requirement['max_work_years'] && $workYears > $requirement['max_work_years']) {
            $match = false;
            $score = 0;
            $reason .= "，超过最多工作年限{$requirement['max_work_years']}年";
        } else {
            $reason .= '，符合工作经验要求';
        }
        
        return ['match' => $match, 'score' => $score, 'reason' => $reason];
    }

    /**
     * 健康状况匹配
     */
    private function matchHealth($resume, $requirement)
    {
        if (!$requirement['health_status']) {
            return null; // 不限健康状况
        }
        
        if (!$resume['health_status']) {
            return ['match' => false, 'score' => 0, 'reason' => '简历未填写健康状况'];
        }
        
        $match = strpos($resume['health_status'], $requirement['health_status']) !== false;
        $score = $match ? 100 : 0;
        $reason = "简历健康状况：{$resume['health_status']}，要求：{$requirement['health_status']}";
        
        return ['match' => $match, 'score' => $score, 'reason' => $reason];
    }

    /**
     * 恐高匹配
     */
    private function matchAfraidHeights($resume, $requirement)
    {
        if (!$requirement['is_afraid_heights']) {
            return null; // 不限恐高
        }
        
        $resumeAfraidHeights = $resume['is_afraid_heights'];
        $match = true;
        $score = 100;
        
        if ($requirement['is_afraid_heights'] == 1) {
            // 要求不能恐高
            $match = $resumeAfraidHeights == 0;
            $reason = $resumeAfraidHeights ? '简历显示恐高，但岗位要求不能恐高' : '不恐高，符合要求';
        } else {
            // 可以恐高
            $reason = '恐高要求符合';
        }
        
        if (!$match) {
            $score = 0;
        }
        
        return ['match' => $match, 'score' => $score, 'reason' => $reason];
    }

    /**
     * 政治面貌匹配
     */
    private function matchPolitical($resume, $requirement)
    {
        if (!$requirement['political_status']) {
            return null; // 不限政治面貌
        }
        
        if (!$resume['political_status']) {
            return ['match' => false, 'score' => 50, 'reason' => '简历未填写政治面貌'];
        }
        
        $match = strpos($resume['political_status'], $requirement['political_status']) !== false;
        $score = $match ? 100 : 50;
        $reason = "简历政治面貌：{$resume['political_status']}，要求：{$requirement['political_status']}";
        
        return ['match' => $match, 'score' => $score, 'reason' => $reason];
    }

    /**
     * 婚姻状况匹配
     */
    private function matchMarital($resume, $requirement)
    {
        if (!$requirement['marital_status']) {
            return null; // 不限婚姻状况
        }
        
        if (!$resume['marital_status']) {
            return ['match' => false, 'score' => 50, 'reason' => '简历未填写婚姻状况'];
        }
        
        $requiredMarital = $requirement['marital_status'] == 1 ? '未婚' : '已婚';
        $match = $resume['marital_status'] == $requiredMarital;
        $score = $match ? 100 : 50;
        $reason = "简历婚姻状况：{$resume['marital_status']}，要求：{$requiredMarital}";
        
        return ['match' => $match, 'score' => $score, 'reason' => $reason];
    }
}
