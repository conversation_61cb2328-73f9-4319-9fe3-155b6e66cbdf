<include file="block/hat" />
<div class="container-fluid">
    <div class="row">
        <include file="block/menu" />
        <div class="col-xs-12 col-sm-9 col-lg-10">
            <ul class="nav nav-tabs">
                <li class="active"><a>岗位配置</a></li>
            </ul>
            <style type='text/css'>.tab-pane {padding:20px 0 20px 0;}</style>
            <div class="main">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <i class="fa fa-cogs"></i> 岗位配置 - {$notice.title}
                        </h4>
                    </div>
                    <div class="panel-body">
                        <div class="alert alert-info">
                            <i class="fa fa-info-circle"></i> 
                            请选择要关联到此招聘公告的岗位。选中的岗位将可以配置筛选要求并进行简历匹配。
                        </div>
                        
                        <form method="post" action="">
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label>
                                            <input type="checkbox" id="selectAll"> 全选/取消全选
                                        </label>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <volist name="postList" id="post">
                                    <div class="col-md-6 col-lg-4">
                                        <div class="panel panel-default">
                                            <div class="panel-body">
                                                <div class="checkbox">
                                                    <label>
                                                        <input type="checkbox" name="post_ids[]" value="{$post.id}" 
                                                               <if condition="in_array($post['id'], $selectedPosts)">checked</if>>
                                                        <strong>{$post.job_name}</strong>
                                                    </label>
                                                </div>
                                                <div class="text-muted">
                                                    <small>
                                                        项目：{$post.project_name|default="未知项目"}<br>
                                                        服务价格：{$post.service_price|default="未设置"}元
                                                    </small>
                                                </div>
                                                <if condition="in_array($post['id'], $selectedPosts)">
                                                    <div class="mt-2">
                                                        <a href="{:U('requirements', ['notice_id' => $notice['id'], 'post_id' => $post['id']])}" 
                                                           class="btn btn-xs btn-info">
                                                            <i class="fa fa-cog"></i> 配置要求
                                                        </a>
                                                    </div>
                                                </if>
                                            </div>
                                        </div>
                                    </div>
                                </volist>
                            </div>
                            
                            <if condition="empty($postList)">
                                <div class="alert alert-warning text-center">
                                    <i class="fa fa-exclamation-triangle"></i> 
                                    暂无可用岗位，请先在项目管理中创建岗位。
                                </div>
                            </if>
                            
                            <div class="form-group">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fa fa-save"></i> 保存配置
                                </button>
                                <a href="{:U('index')}" class="btn btn-default">
                                    <i class="fa fa-arrow-left"></i> 返回
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // 全选功能
    $('#selectAll').change(function() {
        $('input[name="post_ids[]"]').prop('checked', this.checked);
    });
    
    // 单个选择框变化时更新全选状态
    $('input[name="post_ids[]"]').change(function() {
        var total = $('input[name="post_ids[]"]').length;
        var checked = $('input[name="post_ids[]"]:checked').length;
        $('#selectAll').prop('checked', total === checked);
    });
    
    // 初始化全选状态
    var total = $('input[name="post_ids[]"]').length;
    var checked = $('input[name="post_ids[]"]:checked').length;
    $('#selectAll').prop('checked', total === checked && total > 0);
});
</script>

<include file="block/footer" />
