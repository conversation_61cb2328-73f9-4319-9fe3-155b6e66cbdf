<include file="block/hat" />
<div class="container-fluid">
    <div class="row">
        <include file="block/menu" />
        <div class="col-xs-12 col-sm-9 col-lg-10">
            <ul class="nav nav-tabs">
                <li class="active"><a>招聘公告管理</a></li>
            </ul>
            <style type='text/css'>.tab-pane {padding:20px 0 20px 0;}</style>
            
            <!-- 搜索面板 -->
            <div class="panel panel-default">
                <div class="panel-body">
                    <form method="get" class="form-inline" role="form">
                        <div class="form-group">
                            <label>公告标题：</label>
                            <input class="form-control" type="text" name="title" value="{$search.title}" placeholder="请输入公告标题" />
                        </div>
                        <div class="form-group">
                            <label>招聘单位：</label>
                            <input class="form-control" type="text" name="company_name" value="{$search.company_name}" placeholder="请输入招聘单位" />
                        </div>
                        <div class="form-group">
                            <label>状态：</label>
                            <select class="form-control" name="status">
                                <option value="">全部</option>
                                <php>foreach($status_options as $key=>$value){</php>
                                <option value="{$key}" <php>if($key == $search['status']){</php>selected<php>}</php>>{$value.text}</option>
                                <php>}</php>
                            </select>
                        </div>
                        <button type="submit" class="btn btn-primary"><i class="fa fa-search"></i> 搜索</button>
                        <a href="{:U('recruitmentnotice/index')}" class="btn btn-warning">重置</a>
                        <a href="{:U('recruitmentnotice/edit')}" class="btn btn-success"><i class="fa fa-plus"></i> 新增公告</a>
                    </form>
                </div>
            </div>

            <!-- 数据列表 -->
            <form action="{:U('recruitmentnotice/batch')}" method="post" id="listForm">
                <div class="panel panel-default">
                    <div class="panel-body table-responsive">
                        <table class="table table-hover">
                            <thead class="navbar-inner">
                                <tr>
                                    <th width="50">
                                        <label class="form-inline">
                                            <input type="checkbox" class="check-all" style="margin-right: 5px">全选
                                        </label>
                                    </th>
                                    <th width="80">ID</th>
                                    <th>公告标题</th>
                                    <th>招聘单位</th>
                                    <th width="80">关联岗位</th>
                                    <th width="80">状态</th>
                                    <th width="150">创建时间</th>
                                    <th width="200">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <php>if(empty($list)){</php>
                                <tr>
                                    <td colspan="8" class="text-center text-muted">暂无数据</td>
                                </tr>
                                <php>}else{</php>
                                <php>foreach($list as $item){</php>
                                <tr>
                                    <td>
                                        <input type="checkbox" name="ids[]" value="{$item.id}" class="check-item">
                                    </td>
                                    <td>{$item.id}</td>
                                    <td>
                                        <a href="{:U('recruitmentnotice/detail', array('id'=>$item['id']))}" class="text-primary">
                                            {$item.title}
                                        </a>
                                    </td>
                                    <td>{$item.company_name}</td>
                                    <td>
                                        <span class="badge badge-info">{$item.post_count}</span>
                                    </td>
                                    <td>
                                        <span class="label label-{$item.status_info.style}">{$item.status_info.text}</span>
                                    </td>
                                    <td>{$item.create_time_format}</td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="{:U('recruitmentnotice/detail', array('id'=>$item['id']))}" class="btn btn-xs btn-info" title="查看详情">
                                                <i class="fa fa-eye"></i>
                                            </a>
                                            <a href="{:U('recruitmentnotice/edit', array('id'=>$item['id']))}" class="btn btn-xs btn-primary" title="编辑">
                                                <i class="fa fa-edit"></i>
                                            </a>
                                            <a href="{:U('recruitmentnotice/posts', array('id'=>$item['id']))}" class="btn btn-xs btn-warning" title="岗位管理">
                                                <i class="fa fa-tasks"></i>
                                            </a>
                                            <a href="{:U('postrequirements/index', array('notice_id'=>$item['id']))}" class="btn btn-xs btn-success" title="要求配置">
                                                <i class="fa fa-cog"></i>
                                            </a>
                                            <a href="{:U('resumematch/index', array('notice_id'=>$item['id']))}" class="btn btn-xs btn-info" title="匹配结果">
                                                <i class="fa fa-list"></i>
                                            </a>
                                            <php>if($item['status'] == 1){</php>
                                            <a href="{:U('recruitmentnotice/cgstat', array('id'=>$item['id'], 'status'=>0))}" class="btn btn-xs btn-warning js_ajax" title="停用">
                                                <i class="fa fa-pause"></i>
                                            </a>
                                            <php>}else{</php>
                                            <a href="{:U('recruitmentnotice/cgstat', array('id'=>$item['id'], 'status'=>1))}" class="btn btn-xs btn-success js_ajax" title="启用">
                                                <i class="fa fa-play"></i>
                                            </a>
                                            <php>}</php>
                                            <a href="{:U('recruitmentnotice/delete', array('id'=>$item['id']))}" class="btn btn-xs btn-danger js_ajax" confirm="确定要删除这个招聘公告吗？" title="删除">
                                                <i class="fa fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                <php>}</php>
                                <php>}</php>
                            </tbody>
                        </table>
                        
                        <!-- 批量操作 -->
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="form-inline">
                                    <select name="action" class="form-control">
                                        <option value="">批量操作</option>
                                        <option value="enable">批量启用</option>
                                        <option value="disable">批量停用</option>
                                        <option value="delete">批量删除</option>
                                    </select>
                                    <button type="submit" class="btn btn-default" onclick="return confirmBatch()">执行</button>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="pull-right">
                                    {$page}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
$(function(){
    // 全选/取消全选
    $('.check-all').change(function(){
        $('.check-item').prop('checked', $(this).prop('checked'));
    });
    
    // 单项选择
    $('.check-item').change(function(){
        var total = $('.check-item').length;
        var checked = $('.check-item:checked').length;
        $('.check-all').prop('checked', total === checked);
    });
});

function confirmBatch() {
    var checked = $('.check-item:checked').length;
    if (checked === 0) {
        layer.msg('请选择要操作的记录', {icon: 2});
        return false;
    }
    
    var action = $('select[name="action"]').val();
    if (!action) {
        layer.msg('请选择操作类型', {icon: 2});
        return false;
    }
    
    var actionText = $('select[name="action"] option:selected').text();
    return confirm('确定要' + actionText + '选中的 ' + checked + ' 条记录吗？');
}
</script>

<include file="block/footer" />
